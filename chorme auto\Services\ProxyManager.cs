using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using ChromeAutoManager.Models;
using ChromeAutoManager.Core;
using System.Text.RegularExpressions;
using System.Net;

namespace ChromeAutoManager.Services
{
    /// <summary>
    /// Service quản lý proxy
    /// </summary>
    public class ProxyManager
    {
        private readonly List<ProxyInfo> _workingProxies;
        private readonly List<ProxyInfo> _allProxies;
        private readonly object _lock = new object();
        private readonly HttpClient _httpClient;

        public ProxyManager()
        {
            _workingProxies = new List<ProxyInfo>();
            _allProxies = new List<ProxyInfo>();
            _httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(AppConfig.Instance.Proxy.ProxyTimeout) };
        }

        /// <summary>
        /// Số lượng proxy đang hoạt động
        /// </summary>
        public int WorkingProxyCount => _workingProxies.Count(p => p.IsWorking && !p.InUse);

        /// <summary>
        /// Lấy proxy để sử dụng
        /// </summary>
        public ProxyInfo? GetProxy()
        {
            lock (_lock)
            {
                var availableProxy = _workingProxies.FirstOrDefault(p => p.IsWorking && !p.InUse);
                if (availableProxy != null)
                {
                    availableProxy.InUse = true;
                    Logger.Info("Đã cấp phát proxy: {Proxy}", availableProxy.FullAddress);
                }
                return availableProxy;
            }
        }

        /// <summary>
        /// Trả lại proxy sau khi sử dụng
        /// </summary>
        public void ReleaseProxy(ProxyInfo proxy)
        {
            if (proxy != null)
            {
                lock (_lock)
                {
                    proxy.InUse = false;
                    Logger.Info("Đã trả lại proxy: {Proxy}", proxy.FullAddress);
                }
            }
        }

        /// <summary>
        /// Load proxy đã lưu từ file
        /// </summary>
        public int LoadWorkingProxies()
        {
            try
            {
                var proxyFile = AppConfig.Instance.Output.ProxyFile;
                if (!File.Exists(proxyFile))
                {
                    Logger.Warning("File proxy không tồn tại: {File}", proxyFile);
                    return 0;
                }

                var lines = File.ReadAllLines(proxyFile);
                var loadedCount = 0;

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var proxy = ParseProxyString(line);
                    if (proxy != null && proxy.IsValid())
                    {
                        lock (_lock)
                        {
                            if (!_workingProxies.Any(p => p.FullAddress == proxy.FullAddress))
                            {
                                proxy.IsWorking = true;
                                _workingProxies.Add(proxy);
                                loadedCount++;
                            }
                        }
                    }
                }

                Logger.Info("Đã load {Count} proxy từ file", loadedCount);
                return loadedCount;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi load proxy từ file");
                return 0;
            }
        }

        /// <summary>
        /// Fetch và test proxy mới
        /// </summary>
        public async Task<int> FetchAndTestProxiesAsync()
        {
            Logger.Info("Bắt đầu fetch proxy mới...");
            var newProxies = new List<ProxyInfo>();

            // Fetch từ các nguồn
            foreach (var source in AppConfig.Instance.Proxy.ProxySources)
            {
                try
                {
                    var proxies = await FetchProxiesFromSourceAsync(source);
                    newProxies.AddRange(proxies);
                    Logger.Info("Fetch được {Count} proxy từ {Source}", proxies.Count, source);
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "Lỗi khi fetch proxy từ {Source}", source);
                }
            }

            // Loại bỏ duplicate
            var uniqueProxies = newProxies
                .GroupBy(p => p.FullAddress)
                .Select(g => g.First())
                .ToList();

            Logger.Info("Tổng cộng {Count} proxy unique để test", uniqueProxies.Count);

            // Test proxy
            var workingProxies = await TestProxiesAsync(uniqueProxies);

            // Thêm vào danh sách
            lock (_lock)
            {
                foreach (var proxy in workingProxies)
                {
                    if (!_workingProxies.Any(p => p.FullAddress == proxy.FullAddress))
                    {
                        _workingProxies.Add(proxy);
                    }
                }
            }

            // Lưu vào file
            await SaveWorkingProxiesAsync();

            Logger.Info("Đã tìm thấy {Count} proxy hoạt động", workingProxies.Count);
            return workingProxies.Count;
        }

        /// <summary>
        /// Fetch proxy từ một nguồn
        /// </summary>
        private async Task<List<ProxyInfo>> FetchProxiesFromSourceAsync(string source)
        {
            var proxies = new List<ProxyInfo>();

            try
            {
                var response = await _httpClient.GetStringAsync(source);
                var lines = response.Split('\n', StringSplitOptions.RemoveEmptyEntries);

                foreach (var line in lines)
                {
                    var proxy = ParseProxyString(line.Trim());
                    if (proxy != null && proxy.IsValid())
                    {
                        proxies.Add(proxy);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi fetch từ nguồn: {Source}", source);
            }

            return proxies;
        }

        /// <summary>
        /// Parse chuỗi proxy
        /// </summary>
        private ProxyInfo? ParseProxyString(string proxyString)
        {
            try
            {
                // Format: host:port hoặc host:port:username:password
                var parts = proxyString.Split(':');
                if (parts.Length < 2) return null;

                var proxy = new ProxyInfo
                {
                    Host = parts[0].Trim(),
                    Port = int.Parse(parts[1].Trim()),
                    Type = ProxyType.Http
                };

                if (parts.Length >= 4)
                {
                    proxy.Username = parts[2].Trim();
                    proxy.Password = parts[3].Trim();
                }

                return proxy;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Test danh sách proxy
        /// </summary>
        private async Task<List<ProxyInfo>> TestProxiesAsync(List<ProxyInfo> proxies)
        {
            var workingProxies = new List<ProxyInfo>();
            var semaphore = new SemaphoreSlim(AppConfig.Instance.Proxy.MaxProxyTestThreads);

            var tasks = proxies.Select(async proxy =>
            {
                await semaphore.WaitAsync();
                try
                {
                    var isWorking = await TestProxyAsync(proxy);
                    if (isWorking)
                    {
                        lock (workingProxies)
                        {
                            workingProxies.Add(proxy);
                        }
                    }
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
            return workingProxies;
        }

        /// <summary>
        /// Test một proxy
        /// </summary>
        private async Task<bool> TestProxyAsync(ProxyInfo proxy)
        {
            try
            {
                var handler = new HttpClientHandler()
                {
                    Proxy = new WebProxy($"http://{proxy.FullAddress}"),
                    UseProxy = true
                };

                using var client = new HttpClient(handler)
                {
                    Timeout = TimeSpan.FromSeconds(AppConfig.Instance.Proxy.ProxyTimeout)
                };

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var response = await client.GetAsync("http://httpbin.org/ip");
                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    proxy.IsWorking = true;
                    proxy.ResponseTime = (int)stopwatch.ElapsedMilliseconds;
                    proxy.LastTested = DateTime.Now;
                    
                    Logger.LogProxy(proxy.FullAddress, true, proxy.ResponseTime);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogProxy(proxy.FullAddress, false);
                Logger.Debug("Proxy test failed: {Proxy} - {Error}", proxy.FullAddress, ex.Message);
            }

            proxy.IsWorking = false;
            proxy.LastTested = DateTime.Now;
            return false;
        }

        /// <summary>
        /// Lưu proxy hoạt động vào file
        /// </summary>
        private async Task SaveWorkingProxiesAsync()
        {
            try
            {
                var proxyFile = AppConfig.Instance.Output.ProxyFile;
                var lines = _workingProxies
                    .Where(p => p.IsWorking)
                    .Select(p => p.FullAddress)
                    .ToArray();

                await File.WriteAllLinesAsync(proxyFile, lines);
                Logger.Info("Đã lưu {Count} proxy vào file", lines.Length);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi lưu proxy vào file");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
