# 🔧 Troubleshooting Guide - Chrome Auto Manager

## 🚨 **Vấn đề: Tool bị treo và chỉ tới Chrome**

### ✅ **Đã kiểm tra và xác nhận:**
- ✅ Build thành công (31 warnings nhưng không có errors)
- ✅ Executable được tạo: `bin\Debug\net6.0-windows\ChromeAutoManager.exe`
- ✅ Process đang chạy: PID 15396
- ✅ Không có crash hoặc exception

### 🔍 **Nguyên nhân có thể:**

#### **1. GUI Window bị ẩn**
- Window có thể bị minimize hoặc ẩn sau taskbar
- Window có thể mở ở màn hình khác (nếu có multi-monitor)
- Window có thể bị đè bởi window khác

#### **2. Initialization hang**
- C<PERSON> thể bị treo ở việc khởi tạo ProxyManager
- Có thể bị treo ở việc load AppConfig
- <PERSON><PERSON> thể bị treo ở việc tạo UI controls

#### **3. Chrome connection issue**
- Tool có thể đang cố kết nối với Chrome debug port
- Chrome có thể chưa được khởi động với debug mode
- Port 9222 có thể bị occupied

## 🛠️ **Giải pháp:**

### **Bước 1: Kiểm tra GUI Window**
```bash
# Kiểm tra process đang chạy
tasklist | findstr "ChromeAutoManager"

# Nếu có process, thử Alt+Tab để tìm window
# Hoặc click vào taskbar để tìm window
```

### **Bước 2: Kill process và restart**
```bash
# Kill process hiện tại
taskkill /f /im "ChromeAutoManager.exe"

# Restart với debug mode
debug_start.bat
```

### **Bước 3: Kiểm tra Chrome debug mode**
```bash
# Khởi động Chrome với debug port
start_chrome_debug.bat

# Hoặc manual:
chrome.exe --remote-debugging-port=9222 --user-data-dir="chrome_profiles\debug"
```

### **Bước 4: Disable Chrome connection**
- Mở ứng dụng
- Bỏ check "✓ Sử dụng trình duyệt hiện có"
- Thử chạy lại

### **Bước 5: Simple test**
- Chỉ test GUI mà không chạy registration
- Click "🔍 Phân Tích" để test browser connection
- Kiểm tra log messages

## 🎯 **Quick Fixes:**

### **Fix 1: Force show window**
```csharp
// Thêm vào MainForm constructor:
this.WindowState = FormWindowState.Normal;
this.BringToFront();
this.Activate();
this.TopMost = true;
this.TopMost = false;
```

### **Fix 2: Disable auto-initialization**
```csharp
// Comment out trong constructor:
// _proxyManager = new ProxyManager();
// _accountGenerator = new AccountGenerator();
```

### **Fix 3: Add startup logging**
```csharp
// Thêm vào Program.cs:
Console.WriteLine("Starting Chrome Auto Manager...");
MessageBox.Show("Application starting...", "Debug");
```

## 📋 **Checklist để debug:**

### **Environment:**
- [ ] .NET 6.0+ installed
- [ ] Windows Forms enabled
- [ ] All dependencies available
- [ ] No antivirus blocking

### **Application:**
- [ ] Build successful
- [ ] Executable exists
- [ ] Process running
- [ ] No exceptions in logs

### **GUI:**
- [ ] Window visible
- [ ] Controls responsive
- [ ] Events working
- [ ] Layout correct

### **Chrome:**
- [ ] Chrome installed
- [ ] Debug port available
- [ ] Connection working
- [ ] No proxy issues

## 🚀 **Recommended Actions:**

### **Immediate:**
1. **Kill current process**: `taskkill /f /im "ChromeAutoManager.exe"`
2. **Check for window**: Alt+Tab, taskbar, other monitors
3. **Restart with debug**: `debug_start.bat`
4. **Disable Chrome connection**: Uncheck browser option

### **If still hanging:**
1. **Add debug messages** to constructor
2. **Comment out ProxyManager** initialization
3. **Test minimal GUI** first
4. **Check Windows Event Viewer** for errors

### **Long-term:**
1. **Add better error handling** in initialization
2. **Implement timeout** for Chrome connection
3. **Add progress indicators** for startup
4. **Create fallback modes** when Chrome fails

## 📞 **Support Information:**

### **Current Status:**
- ✅ **Build**: Successful with warnings
- ✅ **Process**: Running (PID 15396)
- ❓ **GUI**: Unknown (possibly hidden)
- ❓ **Functionality**: Not tested yet

### **Next Steps:**
1. Find and show the GUI window
2. Test basic functionality
3. Verify layout and controls
4. Test registration process

---

## 💡 **Tips:**

- **Always use debug mode** when troubleshooting
- **Check taskbar** for minimized windows
- **Try different screen resolutions** if window is off-screen
- **Use Task Manager** to monitor resource usage
- **Check logs folder** for error messages

---

*Troubleshooting guide for Chrome Auto Manager GUI issues* 🔧
