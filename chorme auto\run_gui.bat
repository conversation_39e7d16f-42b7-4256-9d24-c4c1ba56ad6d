@echo off
title Chrome Auto Manager - G<PERSON> Launcher

echo.
echo ================================================================
echo                    CHROME AUTO MANAGER
echo                      C# GUI VERSION
echo
echo   Tool tu dong dang ky tai khoan voi giao dien do hoa
echo   Chuyen doi tu Python sang C# WinForms
echo   Theo doi ket qua real-time
echo ================================================================
echo.

echo Dang kiem tra file thuc thi...

if exist "bin\Release\net6.0-windows\ChromeAutoManager.exe" (
    echo [OK] Tim thay file exe da build!
    echo [INFO] Dang khoi dong ung dung...
    echo.
    start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    echo [OK] Ung dung da duoc khoi dong thanh cong!
    echo.
    echo [TIP] Neu ung dung khong hien thi, vui long kiem tra:
    echo    - .NET 6.0 Runtime da duoc cai dat
    echo    - Windows Defender khong chan ung dung
    echo    - Quyen thuc thi file
    echo.
) else (
    echo [ERROR] Khong tim thay file exe!
    echo.
    echo [INFO] Dang thu build ung dung...

    dotnet --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo [ERROR] .NET SDK khong duoc tim thay!
        echo [INFO] Vui long cai dat .NET 6.0 SDK tu: https://dotnet.microsoft.com/download
        goto end
    )

    echo [INFO] Dang restore packages...
    dotnet restore ChromeAutoManager.csproj
    if %errorlevel% neq 0 (
        echo [ERROR] Loi khi restore packages!
        goto end
    )

    echo [INFO] Dang build ung dung...
    dotnet build ChromeAutoManager.csproj --configuration Release
    if %errorlevel% neq 0 (
        echo [ERROR] Build that bai!
        goto end
    )

    echo [OK] Build thanh cong!
    echo [INFO] Dang khoi dong ung dung...
    start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    echo [OK] Ung dung da duoc khoi dong!
)

:end
echo.
echo [GUIDE] Huong dan su dung:
echo    1. Cau hinh so luong tai khoan va luong
echo    2. Chon che do tu dong hoac thu cong
echo    3. Nhan "Phan Tich Form" de kiem tra trang dang ky
echo    4. Nhan "Bat Dau" de khoi dong qua trinh
echo    5. Theo doi ket qua trong giao dien
echo.
echo [FILES] File ket qua se duoc luu tai:
echo    - successful_accounts.txt (tai khoan thanh cong)
echo    - failed_accounts.txt (tai khoan that bai)
echo    - logs\ (file log chi tiet)
echo    - screenshots\ (anh chup man hinh)
echo.
pause
