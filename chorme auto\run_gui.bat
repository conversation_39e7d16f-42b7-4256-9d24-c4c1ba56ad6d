@echo off
chcp 65001 >nul
title Chrome Auto Manager - GUI Launcher

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    CHROME AUTO MANAGER                       ║
echo ║                      C# GUI VERSION                          ║
echo ║                                                              ║
echo ║  🚀 Tool tự động đăng ký tài khoản với giao diện đồ họa     ║
echo ║  🌐 Chuyển đổi từ Python sang C# WinForms                   ║
echo ║  📊 Theo dõi kết quả real-time                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Đang kiểm tra file thực thi...

if exist "bin\Release\net6.0-windows\ChromeAutoManager.exe" (
    echo ✅ Tìm thấy file exe đã build!
    echo 🚀 Đang khởi động ứng dụng...
    echo.
    start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    echo ✅ Ứng dụng đã được khởi động thành công!
    echo.
    echo 💡 Nếu ứng dụng không hiển thị, vui lòng kiểm tra:
    echo    - .NET 6.0 Runtime đã được cài đặt
    echo    - Windows Defender không chặn ứng dụng
    echo    - <PERSON><PERSON><PERSON><PERSON> thực thi file
    echo.
) else (
    echo ❌ Không tìm thấy file exe!
    echo.
    echo 🔨 Đang thử build ứng dụng...
    
    dotnet --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ .NET SDK không được tìm thấy!
        echo 📥 Vui lòng cài đặt .NET 6.0 SDK từ: https://dotnet.microsoft.com/download
        goto end
    )
    
    echo 📦 Đang restore packages...
    dotnet restore ChromeAutoManager.csproj
    if %errorlevel% neq 0 (
        echo ❌ Lỗi khi restore packages!
        goto end
    )
    
    echo 🔨 Đang build ứng dụng...
    dotnet build ChromeAutoManager.csproj --configuration Release
    if %errorlevel% neq 0 (
        echo ❌ Build thất bại!
        goto end
    )
    
    echo ✅ Build thành công!
    echo 🚀 Đang khởi động ứng dụng...
    start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    echo ✅ Ứng dụng đã được khởi động!
)

:end
echo.
echo 📖 Hướng dẫn sử dụng:
echo    1. Cấu hình số lượng tài khoản và luồng
echo    2. Chọn chế độ tự động hoặc thủ công
echo    3. Nhấn "Phân Tích Form" để kiểm tra trang đăng ký
echo    4. Nhấn "Bắt Đầu" để khởi động quá trình
echo    5. Theo dõi kết quả trong giao diện
echo.
echo 📁 File kết quả sẽ được lưu tại:
echo    - successful_accounts.txt (tài khoản thành công)
echo    - failed_accounts.txt (tài khoản thất bại)
echo    - logs\ (file log chi tiết)
echo    - screenshots\ (ảnh chụp màn hình)
echo.
pause
