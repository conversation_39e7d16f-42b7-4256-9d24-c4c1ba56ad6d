# Chrome Auto Manager - C# GUI Version

Tool tự động đăng ký tài khoản với giao diện đồ họa được viết bằng C# WinForms, chuyển đổi từ phiên bản Python.

## 🚀 Tính năng chính

### ✨ Giao diện đồ họa thân thiện
- **GUI hiện đại**: Giao diện WinForms dễ sử dụng
- **Theo dõi real-time**: Hiển thị tiến trình và kết quả trực tiếp
- **Log chi tiết**: <PERSON> dõi toàn bộ quá trình với màu sắc phân biệt
- **Cài đặt linh hoạt**: Form cài đặt với nhiều tùy chọn

### 🤖 Tự động hóa thông minh
- **Phân tích form tự động**: Tự động nhận diện các trường input
- **Đa luồng**: Hỗ trợ chạy nhiều trình duyệt đồng thời
- **Proxy tự động**: T<PERSON><PERSON> kiếm và test proxy miễn phí
- **Retry thông minh**: Tự động thử lại khi gặp lỗi

### 🛡️ Bảo mật và ẩn danh
- **Proxy rotation**: Sử dụng proxy khác nhau cho mỗi tài khoản
- **User Agent rotation**: Thay đổi User Agent ngẫu nhiên
- **Delay ngẫu nhiên**: Mô phỏng hành vi người dùng thật

### 📊 Quản lý kết quả
- **Thống kê real-time**: Hiển thị tỷ lệ thành công/thất bại
- **Lưu kết quả**: Tự động lưu tài khoản thành công và thất bại
- **Screenshot**: Chụp màn hình các bước quan trọng
- **Log chi tiết**: Ghi log đầy đủ với Serilog

## 🏗️ Kiến trúc ứng dụng

```
ChromeAutoManager/
├── Core/                   # Lớp cốt lõi
│   ├── AppConfig.cs       # Cấu hình ứng dụng
│   └── Logger.cs          # Hệ thống logging
├── Models/                # Các model dữ liệu
│   ├── Account.cs         # Model tài khoản
│   ├── ProxyInfo.cs       # Model proxy
│   └── RegistrationResult.cs # Model kết quả đăng ký
├── Services/              # Các service xử lý logic
│   ├── AccountGenerator.cs    # Tạo thông tin tài khoản
│   ├── BrowserManager.cs      # Quản lý trình duyệt
│   ├── ProxyManager.cs        # Quản lý proxy
│   └── RegistrationBot.cs     # Bot đăng ký tự động
├── Forms/                 # Giao diện người dùng
│   ├── MainForm.cs        # Form chính
│   └── SettingsForm.cs    # Form cài đặt
└── Program.cs             # Entry point
```

## 📋 Yêu cầu hệ thống

### Phần mềm cần thiết
- **.NET 6.0 Runtime** hoặc cao hơn
- **Google Chrome** (phiên bản mới nhất)
- **Windows 10/11** (64-bit)

### Thư viện sử dụng
- **Selenium WebDriver 4.15.0** - Tự động hóa trình duyệt
- **Bogus 34.0.2** - Tạo dữ liệu giả
- **Serilog 3.0.1** - Hệ thống logging
- **Newtonsoft.Json 13.0.3** - Xử lý JSON

## 🚀 Hướng dẫn cài đặt

### 1. Clone repository
```bash
git clone <repository-url>
cd "chorme auto"
```

### 2. Build ứng dụng
```bash
# Restore packages
dotnet restore ChromeAutoManager.csproj

# Build ứng dụng
dotnet build ChromeAutoManager.csproj --configuration Release

# Hoặc chạy trực tiếp
dotnet run --project ChromeAutoManager.csproj
```

### 3. Chạy từ Visual Studio
- Mở file `chorme auto.sln` trong Visual Studio
- Chọn **Debug** hoặc **Release** configuration
- Nhấn **F5** để chạy

## 📖 Hướng dẫn sử dụng

### 1. Khởi động ứng dụng
- Chạy file `ChromeAutoManager.exe` hoặc từ Visual Studio
- Ứng dụng sẽ tự động tạo các thư mục cần thiết

### 2. Cấu hình cơ bản
- **Số lượng tài khoản**: Nhập số tài khoản muốn tạo
- **Số luồng**: Số trình duyệt chạy đồng thời (khuyến nghị 1-3)
- **Sử dụng proxy**: Bật/tắt sử dụng proxy
- **Trình duyệt hiện có**: Kết nối với Chrome đang chạy

### 3. Chế độ hoạt động
#### Tự động tạo thông tin
- Chọn **"Tự động tạo thông tin"**
- Ứng dụng sẽ tự động tạo username, password, họ tên ngẫu nhiên

#### Nhập thông tin thủ công
- Chọn **"Nhập thông tin thủ công"**
- Điền thông tin tài khoản muốn đăng ký
- Chỉ tạo được 1 tài khoản trong chế độ này

### 4. Phân tích form đăng ký
- Nhấn **"Phân Tích Form"** để tự động nhận diện các trường input
- Ứng dụng sẽ mở trang đăng ký và tìm các selector phù hợp
- Kết quả hiển thị trong log

### 5. Bắt đầu đăng ký
- Nhấn **"Bắt Đầu"** để khởi động quá trình
- Theo dõi tiến trình qua thanh progress và log
- Kết quả hiển thị real-time trong danh sách

### 6. Cài đặt nâng cao
- Nhấn **"Cài Đặt"** để mở form cấu hình chi tiết
- Điều chỉnh các thông số theo nhu cầu
- Lưu cài đặt để sử dụng cho lần sau

## ⚙️ Cấu hình chi tiết

### Cài đặt trình duyệt
- **Chế độ ẩn**: Chạy Chrome không hiển thị giao diện
- **Kích thước cửa sổ**: Độ phân giải trình duyệt
- **Xoay User Agent**: Thay đổi User Agent ngẫu nhiên
- **Tắt hình ảnh**: Tăng tốc độ load trang
- **Timeout**: Thời gian chờ tối đa

### Cài đặt proxy
- **Nguồn proxy**: Danh sách URL để lấy proxy miễn phí
- **Timeout**: Thời gian test proxy
- **Số luồng test**: Số luồng đồng thời test proxy

### Cài đặt đăng ký
- **Số trình duyệt tối đa**: Giới hạn số browser đồng thời
- **Delay**: Khoảng thời gian nghỉ giữa các hành động
- **Số lần thử lại**: Retry khi gặp lỗi

### Cài đặt tài khoản
- **Domain email**: Danh sách domain để tạo email
- **Độ dài mật khẩu**: Số ký tự của mật khẩu
- **Mã quốc gia**: Prefix số điện thoại
- **Độ tuổi**: Khoảng tuổi ngẫu nhiên

## 📁 Cấu trúc file

```
ChromeAutoManager/
├── config.json              # File cấu hình
├── logs/                     # Thư mục log
│   └── app-{Date}.log       # Log theo ngày
├── screenshots/              # Ảnh chụp màn hình
├── output/                   # Kết quả đầu ra
├── successful_accounts.txt   # Tài khoản thành công
├── failed_accounts.txt       # Tài khoản thất bại
├── working_proxies.txt       # Proxy hoạt động
└── page_source.html         # Source trang đăng ký
```

## 🔧 Troubleshooting

### Lỗi thường gặp

#### 1. Không tìm thấy ChromeDriver
```
Lỗi: ChromeDriver not found
Giải pháp: Cài đặt Chrome phiên bản mới nhất
```

#### 2. Không kết nối được trình duyệt hiện có
```
Lỗi: Could not connect to existing browser
Giải pháp: 
- Khởi động Chrome với debug mode
- Kiểm tra port 9222 có bị chiếm không
```

#### 3. Proxy không hoạt động
```
Lỗi: No working proxies found
Giải pháp:
- Kiểm tra kết nối internet
- Thử nguồn proxy khác
- Tắt proxy và chạy trực tiếp
```

#### 4. Không phân tích được form
```
Lỗi: Cannot analyze registration form
Giải pháp:
- Kiểm tra URL đăng ký
- Thử chạy với trình duyệt hiển thị
- Kiểm tra trang có thay đổi cấu trúc không
```

### Debug mode
- Bật chế độ hiển thị trình duyệt để xem quá trình
- Kiểm tra file log chi tiết trong thư mục `logs/`
- Xem screenshot trong thư mục `screenshots/`

## 🆚 So sánh với phiên bản Python

| Tính năng | Python | C# GUI |
|-----------|--------|--------|
| Giao diện | Console | WinForms GUI |
| Cài đặt | File config | Form cài đặt |
| Theo dõi | Text log | Real-time GUI |
| Đa luồng | ✅ | ✅ |
| Proxy | ✅ | ✅ |
| Screenshot | ✅ | ✅ |
| Logging | Basic | Serilog |
| Platform | Cross-platform | Windows only |

## 🤝 Đóng góp

Mọi đóng góp đều được hoan nghênh! Vui lòng:

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## 📄 License

Dự án này được phát hành dưới MIT License.

## 👨‍💻 Tác giả

**EderGhostVN** - Tool được chuyển đổi từ phiên bản Python sang C# GUI

---

*Lưu ý: Tool này chỉ dành cho mục đích học tập và nghiên cứu. Vui lòng sử dụng có trách nhiệm và tuân thủ các quy định của website.*
