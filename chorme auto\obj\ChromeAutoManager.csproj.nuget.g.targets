﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)selenium.webdriver.chromedriver\119.0.6045.10500\build\Selenium.WebDriver.ChromeDriver.targets" Condition="Exists('$(NuGetPackageRoot)selenium.webdriver.chromedriver\119.0.6045.10500\build\Selenium.WebDriver.ChromeDriver.targets')" />
    <Import Project="$(NuGetPackageRoot)selenium.webdriver\4.15.0\buildTransitive\Selenium.WebDriver.targets" Condition="Exists('$(NuGetPackageRoot)selenium.webdriver\4.15.0\buildTransitive\Selenium.WebDriver.targets')" />
  </ImportGroup>
</Project>