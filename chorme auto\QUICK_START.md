# 🚀 Quick Start Guide - Chrome Auto Manager C#

## Cách chạy nhanh nhất

### 1. Chạy ứng dụng
```bash
# Cách 1: File đơn giản nhất
start.bat

# Cách 2: File với menu
build_and_run.bat

# Cách 3: Launcher với hướng dẫn
run_gui.bat
```

### 2. Sử dụng GUI
1. **Mở ứng dụng** → Giao diện WinForms hiện ra
2. **C<PERSON><PERSON> hình cơ bản**:
   - <PERSON><PERSON> tài khoản: 1-5 (khu<PERSON><PERSON><PERSON> nghị)
   - <PERSON><PERSON> luồng: 1-2 (k<PERSON><PERSON><PERSON><PERSON> nghị)
   - ✅ Sử dụng proxy
   - ✅ Sử dụng trình duyệt hiện có
3. **Chọn chế độ**:
   - 🔘 Tự động tạo thông tin (khuyến nghị)
   - ⚪ Nhập thông tin thủ công
4. **Bắt đầu**:
   - <PERSON>hấn **"Phân Tích Form"** → Ki<PERSON><PERSON> tra trang đăng ký
   - Nhấn **"Bắt Đầu"** → <PERSON> dõi tiến trình

## 📋 Yêu cầu tối thiểu

- ✅ Windows 10/11
- ✅ .NET 6.0 Runtime
- ✅ Google Chrome
- ✅ Kết nối internet

## 🔧 Nếu gặp lỗi

### Lỗi: "ChromeAutoManager.exe not found"
```bash
# Chạy build thủ công
dotnet build ChromeAutoManager.csproj --configuration Release
```

### Lỗi: ".NET SDK not found"
- Tải .NET 6.0 SDK: https://dotnet.microsoft.com/download
- Cài đặt và restart terminal

### Lỗi: "Access denied" hoặc "Permission denied"
- Chạy Command Prompt as Administrator
- Hoặc tắt Windows Defender tạm thời

### Lỗi: Ứng dụng không hiển thị
- Kiểm tra Task Manager → ChromeAutoManager.exe
- Restart máy tính
- Cài đặt lại .NET Runtime

## 📁 File kết quả

Sau khi chạy, kiểm tra các file:
- `successful_accounts.txt` - Tài khoản đăng ký thành công
- `failed_accounts.txt` - Tài khoản thất bại
- `logs/` - Log chi tiết
- `screenshots/` - Ảnh chụp màn hình

## 🎯 Tips sử dụng

1. **Lần đầu chạy**: Dùng 1 tài khoản để test
2. **Proxy**: Để ứng dụng tự động tìm proxy
3. **Trình duyệt**: Đóng Chrome trước khi chạy
4. **Theo dõi**: Xem log màu trong ứng dụng
5. **Dừng**: Nhấn nút "Dừng" để dừng an toàn

## 🆘 Hỗ trợ

Nếu vẫn gặp vấn đề:
1. Kiểm tra file log trong thư mục `logs/`
2. Chụp ảnh màn hình lỗi
3. Kiểm tra Task Manager xem process có chạy không
4. Thử chạy với quyền Administrator

---

**Lưu ý**: Tool chỉ dành cho mục đích học tập và nghiên cứu. Sử dụng có trách nhiệm!
