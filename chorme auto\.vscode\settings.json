{"dotnet.defaultSolution": "chorme auto.sln", "files.exclude": {"**/bin": true, "**/obj": true, "**/__pycache__": true, "**/logs": true, "**/screenshots": true, "**/chrome_profiles": true, "**/temp_profile_*": true, "**/*.log": true, "**/successful_accounts.txt": true, "**/failed_accounts.txt": true, "**/working_proxies.txt": true, "**/page_source*.html": true, "**/debug_page_source.html": true, "**/input_analysis.json": true, "**/username_counter.txt": true, "**/config.json": true}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.cs": "${capture}.Designer.cs, ${capture}.resx", "*.csproj": "*.csproj.user", "*.sln": "*.sln.DotSettings, *.sln.DotSettings.user"}, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "csharp.semanticHighlighting.enabled": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}}