@echo off
title Chrome Auto Manager - Debug Mode
color 0A

echo ================================================================
echo                    CHROME AUTO MANAGER
echo                      DEBUG MODE
echo ================================================================
echo.

echo [DEBUG] Checking .NET installation...
dotnet --version
if %errorlevel% neq 0 (
    echo [ERROR] .NET not found! Please install .NET 6.0 or later.
    pause
    exit /b 1
)
echo [OK] .NET is installed.
echo.

echo [DEBUG] Checking project files...
if not exist "ChromeAutoManager.csproj" (
    echo [ERROR] Project file not found!
    pause
    exit /b 1
)
echo [OK] Project file found.
echo.

echo [DEBUG] Building application...
dotnet build ChromeAutoManager.csproj --configuration Debug --verbosity minimal
if %errorlevel% neq 0 (
    echo [ERROR] Build failed!
    pause
    exit /b 1
)
echo [OK] Build successful.
echo.

echo [DEBUG] Checking executable...
if not exist "bin\Debug\net6.0-windows\ChromeAutoManager.exe" (
    echo [ERROR] Executable not found after build!
    pause
    exit /b 1
)
echo [OK] Executable found.
echo.

echo [DEBUG] Starting application in debug mode...
echo [INFO] If the app crashes, error details will be shown below.
echo.

"bin\Debug\net6.0-windows\ChromeAutoManager.exe"

echo.
echo [DEBUG] Application has exited.
echo [INFO] Check above for any error messages.
echo.
pause
