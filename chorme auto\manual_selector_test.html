<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Selector Test - Chrome Auto Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1976d2;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .section h2 {
            color: #333;
            margin-top: 0;
        }
        .selector-group {
            margin-bottom: 15px;
        }
        .selector-label {
            font-weight: bold;
            color: #555;
            display: block;
            margin-bottom: 5px;
        }
        .selector-list {
            background: #fff;
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .test-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1565c0;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .warning {
            background: #fff3e0;
            color: #ef6c00;
            border: 1px solid #ff9800;
        }
        .instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .code {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Manual Selector Test Tool</h1>
        
        <div class="instructions">
            <h3>📋 Hướng dẫn sử dụng:</h3>
            <ol>
                <li><strong>Mở trang đăng ký</strong> trong tab khác</li>
                <li><strong>Mở Developer Tools</strong> (F12)</li>
                <li><strong>Copy các selectors</strong> bên dưới vào Console</li>
                <li><strong>Chạy test</strong> để xem selector nào hoạt động</li>
                <li><strong>Cập nhật selectors</strong> trong code nếu cần</li>
            </ol>
        </div>

        <div class="section">
            <h2>🎯 Username Field Selectors</h2>
            <div class="selector-group">
                <span class="selector-label">Các selectors để tìm trường username:</span>
                <div class="selector-list">
input[name="username"]<br>
input[name="user"]<br>
input[name="account"]<br>
input[name="login"]<br>
input[name="email"]<br>
input[type="email"]<br>
input[placeholder*="用户"]<br>
input[placeholder*="账号"]<br>
input[placeholder*="username"]<br>
input[placeholder*="email"]<br>
input[placeholder*="tên"]<br>
input[placeholder*="tài khoản"]<br>
input[id*="username"]<br>
input[id*="user"]<br>
input[id*="email"]
                </div>
                <button class="test-button" onclick="testSelectors('username')">🧪 Test Username Selectors</button>
                <div id="username-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="section">
            <h2>🔒 Password Field Selectors</h2>
            <div class="selector-group">
                <span class="selector-label">Các selectors để tìm trường password:</span>
                <div class="selector-list">
input[name="password"]<br>
input[name="pwd"]<br>
input[name="pass"]<br>
input[type="password"]<br>
input[placeholder*="密码"]<br>
input[placeholder*="password"]<br>
input[placeholder*="mật khẩu"]<br>
input[id*="password"]<br>
input[id*="pwd"]
                </div>
                <button class="test-button" onclick="testSelectors('password')">🧪 Test Password Selectors</button>
                <div id="password-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="section">
            <h2>🔒 Confirm Password Selectors</h2>
            <div class="selector-group">
                <span class="selector-label">Các selectors để tìm trường confirm password:</span>
                <div class="selector-list">
input[name="confirmPassword"]<br>
input[name="confirm_password"]<br>
input[name="repassword"]<br>
input[name="password2"]<br>
input[placeholder*="确认密码"]<br>
input[placeholder*="confirm"]<br>
input[placeholder*="nhập lại"]<br>
input[id*="confirm"]<br>
input[id*="repassword"]
                </div>
                <button class="test-button" onclick="testSelectors('confirm_password')">🧪 Test Confirm Password Selectors</button>
                <div id="confirm_password-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="section">
            <h2>👤 Real Name Field Selectors</h2>
            <div class="selector-group">
                <span class="selector-label">Các selectors để tìm trường họ tên:</span>
                <div class="selector-list">
input[name="realName"]<br>
input[name="fullName"]<br>
input[name="name"]<br>
input[name="displayName"]<br>
input[placeholder*="姓名"]<br>
input[placeholder*="真实姓名"]<br>
input[placeholder*="họ tên"]<br>
input[placeholder*="tên thật"]<br>
input[id*="name"]<br>
input[id*="realname"]
                </div>
                <button class="test-button" onclick="testSelectors('real_name')">🧪 Test Real Name Selectors</button>
                <div id="real_name-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="section">
            <h2>📱 Phone Field Selectors</h2>
            <div class="selector-group">
                <span class="selector-label">Các selectors để tìm trường số điện thoại:</span>
                <div class="selector-list">
input[name="phone"]<br>
input[name="mobile"]<br>
input[name="tel"]<br>
input[type="tel"]<br>
input[placeholder*="手机"]<br>
input[placeholder*="电话"]<br>
input[placeholder*="phone"]<br>
input[placeholder*="số điện thoại"]<br>
input[id*="phone"]<br>
input[id*="mobile"]
                </div>
                <button class="test-button" onclick="testSelectors('phone')">🧪 Test Phone Selectors</button>
                <div id="phone-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="section">
            <h2>🚀 Submit Button Selectors</h2>
            <div class="selector-group">
                <span class="selector-label">Các selectors để tìm nút submit:</span>
                <div class="selector-list">
button[type="submit"]<br>
input[type="submit"]<br>
button:contains("注册")<br>
button:contains("提交")<br>
button:contains("Register")<br>
button:contains("Submit")<br>
button:contains("Đăng ký")<br>
.submit-btn<br>
.register-btn<br>
.btn-submit<br>
#submit<br>
#register
                </div>
                <button class="test-button" onclick="testSelectors('submit_button')">🧪 Test Submit Button Selectors</button>
                <div id="submit_button-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="section">
            <h2>📋 Manual Console Test</h2>
            <div class="selector-group">
                <span class="selector-label">Copy đoạn code này vào Console của trang đăng ký:</span>
                <div class="code">
// Test tất cả selectors
function testAllSelectors() {
    const selectors = {
        username: [
            'input[name="username"]', 'input[name="user"]', 'input[name="account"]',
            'input[name="login"]', 'input[name="email"]', 'input[type="email"]',
            'input[placeholder*="username"]', 'input[placeholder*="email"]'
        ],
        password: [
            'input[name="password"]', 'input[name="pwd"]', 'input[type="password"]'
        ],
        submit: [
            'button[type="submit"]', 'input[type="submit"]', '.submit-btn', '.register-btn'
        ]
    };
    
    Object.keys(selectors).forEach(field => {
        console.log(`\n=== Testing ${field} ===`);
        selectors[field].forEach(selector => {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                console.log(`✅ FOUND: ${selector} (${elements.length} elements)`);
                elements.forEach((el, i) => {
                    console.log(`   [${i}] ${el.tagName} - ${el.name || el.id || 'no name/id'}`);
                });
            } else {
                console.log(`❌ NOT FOUND: ${selector}`);
            }
        });
    });
}

// Chạy test
testAllSelectors();
                </div>
                <button class="test-button" onclick="copyToClipboard()">📋 Copy Code to Clipboard</button>
            </div>
        </div>

        <div class="section">
            <h2>🔧 Troubleshooting Tips</h2>
            <div class="instructions">
                <h4>Nếu không tìm thấy selectors:</h4>
                <ul>
                    <li><strong>Kiểm tra HTML source:</strong> View Page Source để xem cấu trúc form</li>
                    <li><strong>Dynamic content:</strong> Form có thể được tạo bằng JavaScript</li>
                    <li><strong>iFrame:</strong> Form có thể nằm trong iframe</li>
                    <li><strong>Shadow DOM:</strong> Form có thể sử dụng Web Components</li>
                    <li><strong>Custom attributes:</strong> Form có thể dùng data-* attributes</li>
                </ul>
                
                <h4>Cách tìm selectors chính xác:</h4>
                <ol>
                    <li>Right-click vào input field → Inspect Element</li>
                    <li>Copy selector từ Developer Tools</li>
                    <li>Test trong Console: <code>document.querySelector('your-selector')</code></li>
                    <li>Cập nhật vào RegistrationBot.cs</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        const selectorData = {
            username: [
                'input[name="username"]', 'input[name="user"]', 'input[name="account"]',
                'input[name="login"]', 'input[name="email"]', 'input[type="email"]',
                'input[placeholder*="username"]', 'input[placeholder*="email"]'
            ],
            password: [
                'input[name="password"]', 'input[name="pwd"]', 'input[type="password"]'
            ],
            confirm_password: [
                'input[name="confirmPassword"]', 'input[name="confirm_password"]', 'input[name="repassword"]'
            ],
            real_name: [
                'input[name="realName"]', 'input[name="fullName"]', 'input[name="name"]'
            ],
            phone: [
                'input[name="phone"]', 'input[name="mobile"]', 'input[type="tel"]'
            ],
            submit_button: [
                'button[type="submit"]', 'input[type="submit"]', '.submit-btn', '.register-btn'
            ]
        };

        function testSelectors(fieldType) {
            const resultDiv = document.getElementById(fieldType + '-result');
            resultDiv.style.display = 'block';
            
            const selectors = selectorData[fieldType];
            let foundSelectors = [];
            let notFoundSelectors = [];
            
            selectors.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        foundSelectors.push(`✅ ${selector} (${elements.length} elements)`);
                    } else {
                        notFoundSelectors.push(`❌ ${selector}`);
                    }
                } catch (e) {
                    notFoundSelectors.push(`❌ ${selector} (invalid)`);
                }
            });
            
            let html = '';
            if (foundSelectors.length > 0) {
                html += '<div class="success"><strong>Found selectors:</strong><br>' + foundSelectors.join('<br>') + '</div>';
            }
            if (notFoundSelectors.length > 0) {
                html += '<div class="error"><strong>Not found:</strong><br>' + notFoundSelectors.join('<br>') + '</div>';
            }
            
            if (foundSelectors.length === 0) {
                html += '<div class="warning"><strong>⚠️ No selectors found!</strong><br>This page may not have ' + fieldType + ' fields, or they use different selectors.</div>';
            }
            
            resultDiv.innerHTML = html;
        }

        function copyToClipboard() {
            const code = document.querySelector('.code').textContent;
            navigator.clipboard.writeText(code).then(() => {
                alert('✅ Code copied to clipboard! Paste it in the registration page console.');
            }).catch(() => {
                alert('❌ Failed to copy. Please select and copy manually.');
            });
        }
    </script>
</body>
</html>
