using System;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json;

namespace ChromeAutoManager.Core
{
    /// <summary>
    /// Cấu hình ứng dụng
    /// </summary>
    public class AppConfig
    {
        private static AppConfig? _instance;
        private static readonly object _lock = new object();

        public static AppConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= LoadConfig();
                    }
                }
                return _instance;
            }
        }

        // URL đăng ký
        public string RegisterUrl { get; set; } = "https://www.13win16.com/home/<USER>";

        // C<PERSON><PERSON> hình trình duyệt
        public BrowserConfig Browser { get; set; } = new BrowserConfig();

        // Cấu hình proxy
        public ProxyConfig Proxy { get; set; } = new ProxyConfig();

        // Cấu hình đăng ký
        public RegistrationConfig Registration { get; set; } = new RegistrationConfig();

        // C<PERSON><PERSON> hình tài khoản
        public AccountConfig Account { get; set; } = new AccountConfig();

        // Cấu hình file output
        public OutputConfig Output { get; set; } = new OutputConfig();

        // Selectors cho form đăng ký
        public FormSelectors FormSelectors { get; set; } = new FormSelectors();

        // Cấu hình delay
        public DelayConfig Delay { get; set; } = new DelayConfig();

        private static AppConfig LoadConfig()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    return JsonConvert.DeserializeObject<AppConfig>(json) ?? new AppConfig();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi load config: {ex.Message}");
            }

            return new AppConfig();
        }

        public void SaveConfig()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
                string json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(configPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi save config: {ex.Message}");
            }
        }
    }

    public class BrowserConfig
    {
        public bool Headless { get; set; } = false;
        public int WindowWidth { get; set; } = 1366;
        public int WindowHeight { get; set; } = 768;
        public bool UserAgentRotation { get; set; } = true;
        public bool DisableImages { get; set; } = false;
        public bool DisableCss { get; set; } = false;
        public int PageLoadTimeout { get; set; } = 30;
        public int ImplicitWait { get; set; } = 10;
        public bool UseExistingBrowser { get; set; } = true;
        public int DebugPort { get; set; } = 9222;
    }

    public class ProxyConfig
    {
        public bool UseProxy { get; set; } = true;
        public List<string> ProxySources { get; set; } = new List<string>
        {
            "https://www.proxy-list.download/api/v1/get?type=http",
            "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
            "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt"
        };
        public int ProxyTimeout { get; set; } = 10;
        public int MaxProxyTestThreads { get; set; } = 50;
    }

    public class RegistrationConfig
    {
        public int MaxConcurrentBrowsers { get; set; } = 5;
        public int DelayBetweenActionsMin { get; set; } = 1;
        public int DelayBetweenActionsMax { get; set; } = 3;
        public int RetryAttempts { get; set; } = 3;
        public int SuccessDelayMin { get; set; } = 5;
        public int SuccessDelayMax { get; set; } = 10;
    }

    public class AccountConfig
    {
        public List<string> EmailDomains { get; set; } = new List<string>
        {
            "gmail.com", "yahoo.com", "hotmail.com", "outlook.com",
            "protonmail.com", "tempmail.org", "10minutemail.com"
        };
        public int PasswordLength { get; set; } = 12;
        public List<string> PhoneCountryCodes { get; set; } = new List<string> { "+84", "+1", "+44", "+86", "+91" };
        public int MinAge { get; set; } = 18;
        public int MaxAge { get; set; } = 65;
    }

    public class OutputConfig
    {
        public string SuccessFile { get; set; } = "successful_accounts.txt";
        public string FailedFile { get; set; } = "failed_accounts.txt";
        public string ProxyFile { get; set; } = "working_proxies.txt";
        public string LogFile { get; set; } = "registration.log";
        public string ScreenshotFolder { get; set; } = "screenshots";
    }

    public class FormSelectors
    {
        public string UsernamePhone { get; set; } = "input[placeholder*=\"Nhập Số điện thoại/Tên Đăng Nhập\"], input[name=\"username\"], input[name=\"phone\"]";
        public string Password { get; set; } = "input[placeholder*=\"Mật khẩu\"], input[name=\"password\"], input[type=\"password\"]";
        public string ConfirmPassword { get; set; } = "input[placeholder*=\"Vui lòng xác nhận lại mật khẩu\"], input[name=\"confirmPassword\"], input[name=\"confirm_password\"]";
        public string RealName { get; set; } = "input[placeholder*=\"Họ Tên Thật\"], input[name=\"realName\"], input[name=\"fullName\"]";
        public string SubmitButton { get; set; } = "button:contains(\"ĐĂNG KÝ\"), button[type=\"submit\"], input[type=\"submit\"], .submit-btn";
        public string TermsCheckbox { get; set; } = "input[type=\"checkbox\"]";
        public string Captcha { get; set; } = "input[name=\"captcha\"], input[name=\"code\"], input[placeholder*=\"验证码\"]";
    }

    public class DelayConfig
    {
        public int PageLoadWait { get; set; } = 5;
        public int ElementWait { get; set; } = 10;
        public double AfterInputMin { get; set; } = 0.5;
        public double AfterInputMax { get; set; } = 1.5;
        public int AfterClickMin { get; set; } = 1;
        public int AfterClickMax { get; set; } = 2;
        public int AfterSubmit { get; set; } = 5;
        public int RetryDelay { get; set; } = 3;
    }
}
