# 🚀 Chrome Auto Manager - Dual Version

Tool tự động đăng ký tài khoản với **2 phiên bản**: Python Console và C# GUI.

## 🎯 Chọn phiên bản phù hợp

### 🐍 Python Version (Original)
- ✅ **Cross-platform** (Windows, Mac, Linux)
- ✅ **Lightweight** và nhanh
- ✅ **Console interface** đơn giản
- ✅ **Ổn định** và đã test kỹ

### 🖥️ C# GUI Version (New) - **KHUYẾN NGHỊ**
- ✅ **Windows Forms GUI** thân thiện
- ✅ **Real-time progress** tracking
- ✅ **Visual feedback** với màu sắc
- ✅ **Settings form** chi tiết
- ✅ **Better error handling**

## ✨ Tính năng chung

- 🤖 **Tự động đăng ký**: Tự động điền form và submit
- 🌐 **Đa proxy**: Sử dụng proxy khác nhau cho mỗi tài khoản
- 🧵 **Đa luồng**: Chạy nhiều trình duyệt đồng thời
- 📊 **<PERSON> dõi real-time**: Hiển thị tiến trình và kết quả
- 🔄 **Retry thông minh**: Tự động thử lại khi gặp lỗi
- 📸 **Screenshot**: Chụp ảnh các bước quan trọng
- 📝 **Logging**: Ghi log chi tiết toàn bộ quá trình
- 🔍 **Form analysis**: Tự động phân tích form đăng ký

## 📋 Yêu cầu hệ thống

### Python Version
- Python 3.7+
- Chrome Browser
- Internet connection

### C# GUI Version
- Windows 10/11
- .NET 6.0 Runtime
- Chrome Browser
- Internet connection

## 🚀 Quick Start

### 🖥️ C# GUI Version (Khuyến nghị)
```bash
# Cách nhanh nhất
start.bat

# Hoặc với menu
build_and_run.bat
```

### 🐍 Python Version
```bash
# Cài đặt dependencies
pip install -r requirements.txt

# Chạy tool
python start_tool.py
```

## 📖 Hướng dẫn sử dụng

### 🖥️ C# GUI Version

1. **Khởi động ứng dụng:**
   - Chạy `start.bat` hoặc `build_and_run.bat`
   - Giao diện GUI sẽ hiện ra

2. **Cấu hình trong GUI:**
   - **Số tài khoản**: 1-10 (khuyến nghị 1-3)
   - **Số luồng**: 1-5 (khuyến nghị 1-2)
   - **✅ Sử dụng proxy**: Tự động tìm proxy
   - **✅ Trình duyệt hiện có**: Kết nối Chrome đang chạy

3. **Chọn chế độ:**
   - **🔘 Tự động**: Tool tạo thông tin ngẫu nhiên
   - **⚪ Thủ công**: Nhập username/password/họ tên

4. **Thực hiện:**
   - Nhấn **"Phân Tích Form"** → Kiểm tra trang đăng ký
   - Nhấn **"Bắt Đầu"** → Theo dõi progress real-time

### 🐍 Python Version

#### 🎯 Cách 1: Sử dụng trình duyệt hiện có (Khuyến nghị)

1. **Khởi động Chrome debug mode:**
```bash
start_chrome_debug.bat
```

2. **Chạy tool:**
```bash
python main.py
```

3. **Chọn cấu hình:**
   - Chế độ: 1 (Tự động) hoặc 2 (Thủ công)
   - Nếu chọn thủ công: nhập tên đăng nhập, mật khẩu, họ tên
   - Sử dụng trình duyệt hiện có: y
   - Sử dụng proxy: y/n

### 🎯 Cách 2: Tạo trình duyệt mới

1. **Chạy tool:**
```bash
python main.py
```

2. **Chọn cấu hình:**
   - Chế độ: 1 (Tự động) hoặc 2 (Thủ công)
   - Số lượng tài khoản muốn tạo
   - Số trình duyệt chạy đồng thời (1-5)
   - Sử dụng trình duyệt hiện có: n
   - Sử dụng proxy: y/n

3. **Tool sẽ tự động:**
   - Tìm và test proxy miễn phí
   - Phân tích form đăng ký
   - Tạo thông tin tài khoản ngẫu nhiên hoặc sử dụng thông tin thủ công
   - Đăng ký tài khoản với proxy khác nhau

## 📊 So sánh 2 phiên bản

| Tính năng | Python | C# GUI |
|-----------|--------|--------|
| **Giao diện** | Console | WinForms GUI ✨ |
| **Platform** | Cross-platform | Windows only |
| **Cài đặt** | pip install | .NET Runtime |
| **Cấu hình** | File config.py | GUI Form ✨ |
| **Theo dõi** | Text log | Progress bar + Log màu ✨ |
| **Error handling** | Basic | Advanced ✨ |
| **User experience** | Technical | User-friendly ✨ |
| **Performance** | Good | Better ✨ |

## 📁 Cấu trúc project

```
chrome-auto/
├── 🐍 Python Files (Original)
│   ├── main.py                 # File chính Python
│   ├── browser_manager.py      # Quản lý trình duyệt
│   ├── proxy_manager.py        # Quản lý proxy
│   ├── registration_bot.py     # Bot đăng ký
│   ├── account_generator.py    # Tạo thông tin tài khoản
│   ├── config.py              # Cấu hình Python
│   └── requirements.txt       # Python dependencies
├── 🖥️ C# Files (New)
│   ├── ChromeAutoManager.csproj # C# project file
│   ├── Program.cs              # Entry point
│   ├── Core/                   # Core classes
│   ├── Models/                 # Data models
│   ├── Services/               # Business logic
│   ├── Forms/                  # GUI forms
│   └── bin/Release/            # Built executable
├── 🚀 Launchers
│   ├── start.bat              # Quick start C#
│   ├── build_and_run.bat      # Build & run C#
│   ├── run_gui.bat            # GUI launcher
│   └── start_tool.py          # Python launcher
└── 📁 Output
    ├── successful_accounts.txt
    ├── failed_accounts.txt
    ├── working_proxies.txt
    ├── logs/                   # Detailed logs
    └── screenshots/            # Screenshots
```

## ⚙️ Cấu hình

### Python Version
Chỉnh sửa file `config.py`:
- **BROWSER_CONFIG**: Cấu hình trình duyệt
- **PROXY_CONFIG**: Cấu hình proxy
- **REGISTRATION_CONFIG**: Cấu hình đăng ký
- **ACCOUNT_CONFIG**: Cấu hình thông tin tài khoản

### C# GUI Version
- **GUI Settings Form**: Cấu hình qua giao diện
- **config.json**: File cấu hình tự động tạo
- **Real-time adjustment**: Thay đổi cài đặt ngay lập tức

## 📊 Kết quả

Cả 2 phiên bản đều tạo các file kết quả:

- `successful_accounts.txt`: Danh sách tài khoản đăng ký thành công
- `failed_accounts.txt`: Danh sách tài khoản thất bại
- `working_proxies.txt`: Danh sách proxy hoạt động
- `logs/app-{date}.log`: Log chi tiết (C# version)
- `registration.log`: Log chi tiết (Python version)
- `page_source.html`: Source code trang đăng ký
- `screenshots/`: Thư mục chứa ảnh chụp màn hình

## 🔧 Troubleshooting

### Lỗi thường gặp:

1. **ChromeDriver không tìm thấy:**
   - Tool sẽ tự động tải ChromeDriver
   - Đảm bảo Chrome Browser đã cài đặt

2. **Không tìm thấy proxy:**
   - Chọn "n" khi hỏi sử dụng proxy
   - Hoặc chờ tool tìm proxy mới

3. **Form đăng ký thay đổi:**
   - Tool sẽ tự động phân tích form
   - Kiểm tra file `page_source.html` và `registration_form.png`

4. **Captcha:**
   - Tool sẽ dừng để user nhập captcha thủ công
   - Nhấn Enter sau khi nhập xong

## ⚠️ Lưu ý

- Tool chỉ dành cho mục đích học tập và test
- Tuân thủ Terms of Service của website
- Sử dụng có trách nhiệm
- Không spam hoặc lạm dụng

## 🤝 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra file log: `registration.log`
2. Xem screenshot: `registration_form.png`
3. Kiểm tra source code: `page_source.html`

## 📝 Changelog

### v1.0.0
- Tính năng cơ bản đăng ký tài khoản
- Hỗ trợ proxy miễn phí
- Đa luồng
- Tự động phân tích form
