using System;
using System.Drawing;
using System.Windows.Forms;
using ChromeAutoManager.Core;

namespace ChromeAutoManager.Forms
{
    /// <summary>
    /// Form cài đặt ứng dụng
    /// </summary>
    public partial class SettingsForm : Form
    {
        private TabControl tabControl;
        private TabPage tabBrowser;
        private TabPage tabProxy;
        private TabPage tabRegistration;
        private TabPage tabAccount;
        private Button btnSave;
        private Button btnCancel;
        private Button btnReset;

        // Browser settings
        private CheckBox chkHeadless;
        private NumericUpDown numWindowWidth;
        private NumericUpDown numWindowHeight;
        private CheckBox chkUserAgentRotation;
        private CheckBox chkDisableImages;
        private NumericUpDown numPageLoadTimeout;
        private NumericUpDown numImplicitWait;
        private NumericUpDown numDebugPort;

        // Proxy settings
        private CheckBox chkUseProxy;
        private TextBox txtProxySources;
        private NumericUpDown numProxyTimeout;
        private NumericUpDown numMaxProxyTestThreads;

        // Registration settings
        private NumericUpDown numMaxConcurrentBrowsers;
        private NumericUpDown numDelayMin;
        private NumericUpDown numDelayMax;
        private NumericUpDown numRetryAttempts;

        // Account settings
        private TextBox txtEmailDomains;
        private NumericUpDown numPasswordLength;
        private TextBox txtPhoneCountryCodes;
        private NumericUpDown numMinAge;
        private NumericUpDown numMaxAge;

        public SettingsForm()
        {
            InitializeComponent();
            LoadSettings();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "Cài Đặt";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Tab control
            tabControl = new TabControl
            {
                Location = new Point(10, 10),
                Size = new Size(570, 400)
            };

            // Create tabs
            CreateBrowserTab();
            CreateProxyTab();
            CreateRegistrationTab();
            CreateAccountTab();

            // Buttons
            btnSave = new Button
            {
                Text = "Lưu",
                Location = new Point(350, 420),
                Size = new Size(75, 25),
                DialogResult = DialogResult.OK
            };

            btnCancel = new Button
            {
                Text = "Hủy",
                Location = new Point(435, 420),
                Size = new Size(75, 25),
                DialogResult = DialogResult.Cancel
            };

            btnReset = new Button
            {
                Text = "Mặc Định",
                Location = new Point(520, 420),
                Size = new Size(75, 25)
            };

            // Event handlers
            btnSave.Click += BtnSave_Click;
            btnReset.Click += BtnReset_Click;

            // Add controls
            this.Controls.AddRange(new Control[] { tabControl, btnSave, btnCancel, btnReset });

            this.ResumeLayout(false);
        }

        private void CreateBrowserTab()
        {
            tabBrowser = new TabPage("Trình Duyệt");

            // Headless
            chkHeadless = new CheckBox
            {
                Text = "Chạy ẩn (Headless)",
                Location = new Point(10, 20),
                Size = new Size(150, 20)
            };

            // Window size
            var lblWindowSize = new Label
            {
                Text = "Kích thước cửa sổ:",
                Location = new Point(10, 50),
                Size = new Size(120, 20)
            };

            numWindowWidth = new NumericUpDown
            {
                Minimum = 800,
                Maximum = 1920,
                Location = new Point(140, 48),
                Size = new Size(60, 20)
            };

            var lblX = new Label
            {
                Text = "x",
                Location = new Point(210, 50),
                Size = new Size(10, 20)
            };

            numWindowHeight = new NumericUpDown
            {
                Minimum = 600,
                Maximum = 1080,
                Location = new Point(230, 48),
                Size = new Size(60, 20)
            };

            // User agent rotation
            chkUserAgentRotation = new CheckBox
            {
                Text = "Xoay User Agent",
                Location = new Point(10, 80),
                Size = new Size(150, 20)
            };

            // Disable images
            chkDisableImages = new CheckBox
            {
                Text = "Tắt hình ảnh",
                Location = new Point(170, 80),
                Size = new Size(100, 20)
            };

            // Page load timeout
            var lblPageLoadTimeout = new Label
            {
                Text = "Timeout load trang (s):",
                Location = new Point(10, 110),
                Size = new Size(130, 20)
            };

            numPageLoadTimeout = new NumericUpDown
            {
                Minimum = 10,
                Maximum = 120,
                Location = new Point(150, 108),
                Size = new Size(60, 20)
            };

            // Implicit wait
            var lblImplicitWait = new Label
            {
                Text = "Thời gian chờ (s):",
                Location = new Point(10, 140),
                Size = new Size(110, 20)
            };

            numImplicitWait = new NumericUpDown
            {
                Minimum = 5,
                Maximum = 60,
                Location = new Point(130, 138),
                Size = new Size(60, 20)
            };

            // Debug port
            var lblDebugPort = new Label
            {
                Text = "Debug port:",
                Location = new Point(10, 170),
                Size = new Size(80, 20)
            };

            numDebugPort = new NumericUpDown
            {
                Minimum = 9000,
                Maximum = 9999,
                Location = new Point(100, 168),
                Size = new Size(60, 20)
            };

            tabBrowser.Controls.AddRange(new Control[]
            {
                chkHeadless, lblWindowSize, numWindowWidth, lblX, numWindowHeight,
                chkUserAgentRotation, chkDisableImages, lblPageLoadTimeout, numPageLoadTimeout,
                lblImplicitWait, numImplicitWait, lblDebugPort, numDebugPort
            });

            tabControl.TabPages.Add(tabBrowser);
        }

        private void CreateProxyTab()
        {
            tabProxy = new TabPage("Proxy");

            // Use proxy
            chkUseProxy = new CheckBox
            {
                Text = "Sử dụng proxy",
                Location = new Point(10, 20),
                Size = new Size(120, 20)
            };

            // Proxy sources
            var lblProxySources = new Label
            {
                Text = "Nguồn proxy (mỗi dòng một URL):",
                Location = new Point(10, 50),
                Size = new Size(200, 20)
            };

            txtProxySources = new TextBox
            {
                Location = new Point(10, 75),
                Size = new Size(540, 100),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            // Proxy timeout
            var lblProxyTimeout = new Label
            {
                Text = "Timeout proxy (s):",
                Location = new Point(10, 190),
                Size = new Size(110, 20)
            };

            numProxyTimeout = new NumericUpDown
            {
                Minimum = 5,
                Maximum = 60,
                Location = new Point(130, 188),
                Size = new Size(60, 20)
            };

            // Max proxy test threads
            var lblMaxProxyTestThreads = new Label
            {
                Text = "Số luồng test proxy:",
                Location = new Point(10, 220),
                Size = new Size(120, 20)
            };

            numMaxProxyTestThreads = new NumericUpDown
            {
                Minimum = 10,
                Maximum = 100,
                Location = new Point(140, 218),
                Size = new Size(60, 20)
            };

            tabProxy.Controls.AddRange(new Control[]
            {
                chkUseProxy, lblProxySources, txtProxySources,
                lblProxyTimeout, numProxyTimeout, lblMaxProxyTestThreads, numMaxProxyTestThreads
            });

            tabControl.TabPages.Add(tabProxy);
        }

        private void CreateRegistrationTab()
        {
            tabRegistration = new TabPage("Đăng Ký");

            // Max concurrent browsers
            var lblMaxConcurrentBrowsers = new Label
            {
                Text = "Số trình duyệt tối đa:",
                Location = new Point(10, 20),
                Size = new Size(130, 20)
            };

            numMaxConcurrentBrowsers = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 20,
                Location = new Point(150, 18),
                Size = new Size(60, 20)
            };

            // Delay between actions
            var lblDelay = new Label
            {
                Text = "Delay giữa các hành động (s):",
                Location = new Point(10, 50),
                Size = new Size(160, 20)
            };

            numDelayMin = new NumericUpDown
            {
                Minimum = 0.1m,
                Maximum = 10,
                DecimalPlaces = 1,
                Increment = 0.1m,
                Location = new Point(180, 48),
                Size = new Size(60, 20)
            };

            var lblTo = new Label
            {
                Text = "đến",
                Location = new Point(250, 50),
                Size = new Size(25, 20)
            };

            numDelayMax = new NumericUpDown
            {
                Minimum = 0.1m,
                Maximum = 10,
                DecimalPlaces = 1,
                Increment = 0.1m,
                Location = new Point(280, 48),
                Size = new Size(60, 20)
            };

            // Retry attempts
            var lblRetryAttempts = new Label
            {
                Text = "Số lần thử lại:",
                Location = new Point(10, 80),
                Size = new Size(90, 20)
            };

            numRetryAttempts = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 10,
                Location = new Point(110, 78),
                Size = new Size(60, 20)
            };

            tabRegistration.Controls.AddRange(new Control[]
            {
                lblMaxConcurrentBrowsers, numMaxConcurrentBrowsers,
                lblDelay, numDelayMin, lblTo, numDelayMax,
                lblRetryAttempts, numRetryAttempts
            });

            tabControl.TabPages.Add(tabRegistration);
        }

        private void CreateAccountTab()
        {
            tabAccount = new TabPage("Tài Khoản");

            // Email domains
            var lblEmailDomains = new Label
            {
                Text = "Domain email (phân cách bằng dấu phẩy):",
                Location = new Point(10, 20),
                Size = new Size(220, 20)
            };

            txtEmailDomains = new TextBox
            {
                Location = new Point(10, 45),
                Size = new Size(540, 20)
            };

            // Password length
            var lblPasswordLength = new Label
            {
                Text = "Độ dài mật khẩu:",
                Location = new Point(10, 80),
                Size = new Size(100, 20)
            };

            numPasswordLength = new NumericUpDown
            {
                Minimum = 6,
                Maximum = 32,
                Location = new Point(120, 78),
                Size = new Size(60, 20)
            };

            // Phone country codes
            var lblPhoneCountryCodes = new Label
            {
                Text = "Mã quốc gia điện thoại (phân cách bằng dấu phẩy):",
                Location = new Point(10, 110),
                Size = new Size(280, 20)
            };

            txtPhoneCountryCodes = new TextBox
            {
                Location = new Point(10, 135),
                Size = new Size(540, 20)
            };

            // Age range
            var lblAgeRange = new Label
            {
                Text = "Độ tuổi:",
                Location = new Point(10, 170),
                Size = new Size(60, 20)
            };

            numMinAge = new NumericUpDown
            {
                Minimum = 16,
                Maximum = 80,
                Location = new Point(80, 168),
                Size = new Size(60, 20)
            };

            var lblToAge = new Label
            {
                Text = "đến",
                Location = new Point(150, 170),
                Size = new Size(25, 20)
            };

            numMaxAge = new NumericUpDown
            {
                Minimum = 16,
                Maximum = 80,
                Location = new Point(180, 168),
                Size = new Size(60, 20)
            };

            tabAccount.Controls.AddRange(new Control[]
            {
                lblEmailDomains, txtEmailDomains, lblPasswordLength, numPasswordLength,
                lblPhoneCountryCodes, txtPhoneCountryCodes,
                lblAgeRange, numMinAge, lblToAge, numMaxAge
            });

            tabControl.TabPages.Add(tabAccount);
        }

        private void LoadSettings()
        {
            var config = AppConfig.Instance;

            // Browser settings
            chkHeadless.Checked = config.Browser.Headless;
            numWindowWidth.Value = config.Browser.WindowWidth;
            numWindowHeight.Value = config.Browser.WindowHeight;
            chkUserAgentRotation.Checked = config.Browser.UserAgentRotation;
            chkDisableImages.Checked = config.Browser.DisableImages;
            numPageLoadTimeout.Value = config.Browser.PageLoadTimeout;
            numImplicitWait.Value = config.Browser.ImplicitWait;
            numDebugPort.Value = config.Browser.DebugPort;

            // Proxy settings
            chkUseProxy.Checked = config.Proxy.UseProxy;
            txtProxySources.Text = string.Join(Environment.NewLine, config.Proxy.ProxySources);
            numProxyTimeout.Value = config.Proxy.ProxyTimeout;
            numMaxProxyTestThreads.Value = config.Proxy.MaxProxyTestThreads;

            // Registration settings
            numMaxConcurrentBrowsers.Value = config.Registration.MaxConcurrentBrowsers;
            numDelayMin.Value = config.Registration.DelayBetweenActionsMin;
            numDelayMax.Value = config.Registration.DelayBetweenActionsMax;
            numRetryAttempts.Value = config.Registration.RetryAttempts;

            // Account settings
            txtEmailDomains.Text = string.Join(", ", config.Account.EmailDomains);
            numPasswordLength.Value = config.Account.PasswordLength;
            txtPhoneCountryCodes.Text = string.Join(", ", config.Account.PhoneCountryCodes);
            numMinAge.Value = config.Account.MinAge;
            numMaxAge.Value = config.Account.MaxAge;
        }

        private void SaveSettings()
        {
            var config = AppConfig.Instance;

            // Browser settings
            config.Browser.Headless = chkHeadless.Checked;
            config.Browser.WindowWidth = (int)numWindowWidth.Value;
            config.Browser.WindowHeight = (int)numWindowHeight.Value;
            config.Browser.UserAgentRotation = chkUserAgentRotation.Checked;
            config.Browser.DisableImages = chkDisableImages.Checked;
            config.Browser.PageLoadTimeout = (int)numPageLoadTimeout.Value;
            config.Browser.ImplicitWait = (int)numImplicitWait.Value;
            config.Browser.DebugPort = (int)numDebugPort.Value;

            // Proxy settings
            config.Proxy.UseProxy = chkUseProxy.Checked;
            config.Proxy.ProxySources = new List<string>(txtProxySources.Text.Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries));
            config.Proxy.ProxyTimeout = (int)numProxyTimeout.Value;
            config.Proxy.MaxProxyTestThreads = (int)numMaxProxyTestThreads.Value;

            // Registration settings
            config.Registration.MaxConcurrentBrowsers = (int)numMaxConcurrentBrowsers.Value;
            config.Registration.DelayBetweenActionsMin = (int)numDelayMin.Value;
            config.Registration.DelayBetweenActionsMax = (int)numDelayMax.Value;
            config.Registration.RetryAttempts = (int)numRetryAttempts.Value;

            // Account settings
            config.Account.EmailDomains = new List<string>(txtEmailDomains.Text.Split(new[] { ", " }, StringSplitOptions.RemoveEmptyEntries));
            config.Account.PasswordLength = (int)numPasswordLength.Value;
            config.Account.PhoneCountryCodes = new List<string>(txtPhoneCountryCodes.Text.Split(new[] { ", " }, StringSplitOptions.RemoveEmptyEntries));
            config.Account.MinAge = (int)numMinAge.Value;
            config.Account.MaxAge = (int)numMaxAge.Value;

            config.SaveConfig();
        }

        private void BtnSave_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveSettings();
                MessageBox.Show("Cài đặt đã được lưu thành công!", "Thông báo", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi lưu cài đặt: {ex.Message}", "Lỗi", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnReset_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("Bạn có muốn khôi phục cài đặt mặc định?", "Xác nhận", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                // Reset to default and reload
                var defaultConfig = new AppConfig();
                LoadSettings();
            }
        }
    }
}
