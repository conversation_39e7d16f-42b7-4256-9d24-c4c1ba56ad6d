[2025-05-28 12:21:14.645 +07:00 INF] <PERSON><PERSON> initialized successfully
[2025-05-28 12:21:14.672 +07:00 INF] Ứng dụng Chrome Auto Manager đã khởi động
[2025-05-28 12:29:02.558 +07:00 INF] <PERSON><PERSON> initialized successfully
[2025-05-28 12:29:02.611 +07:00 INF] Ứng dụng Chrome Auto Manager đã khởi động
[2025-05-28 13:05:10.562 +07:00 INF] <PERSON><PERSON> initialized successfully
[2025-05-28 13:05:10.585 +07:00 INF] Ứng dụng Chrome Auto Manager đã khởi động
[2025-05-28 13:05:23.729 +07:00 INF] Đ<PERSON> load 112 proxy từ file
[2025-05-28 13:06:24.254 +07:00 ERR] [BROWSER] Connect to existing browser - FAILED: The HTTP request to the remote WebDriver server for URL http://localhost:63562/session timed out after 60 seconds.
[2025-05-28 13:06:24.255 +07:00 INF] Không thể kết nối browser hiện có, tạo browser mới...
[2025-05-28 13:06:25.562 +07:00 INF] [BROWSER] Create new browser - SUCCESS
[2025-05-28 13:06:27.073 +07:00 INF] [BROWSER] Navigate - SUCCESS: https://www.13win16.com/home/<USER>
[2025-05-28 13:06:42.158 +07:00 WRN] Không tìm thấy element: input[name="username"]
[2025-05-28 13:06:52.219 +07:00 WRN] Không tìm thấy element: input[name="user"]
[2025-05-28 13:07:02.276 +07:00 WRN] Không tìm thấy element: input[name="account"]
[2025-05-28 13:07:12.305 +07:00 WRN] Không tìm thấy element: input[placeholder*="用户"]
[2025-05-28 13:07:22.334 +07:00 WRN] Không tìm thấy element: input[placeholder*="账号"]
[2025-05-28 13:07:32.349 +07:00 WRN] Không tìm thấy element: input[placeholder*="username"]
[2025-05-28 13:07:42.409 +07:00 WRN] Không tìm thấy element: input[id*="username"]
[2025-05-28 13:07:52.460 +07:00 WRN] Không tìm thấy element: input[id*="user"]
[2025-05-28 13:08:02.538 +07:00 WRN] Không tìm thấy element: input[name="password"]
[2025-05-28 13:08:12.567 +07:00 WRN] Không tìm thấy element: input[name="pwd"]
[2025-05-28 13:11:35.225 +07:00 INF] Logger initialized successfully
[2025-05-28 13:11:35.250 +07:00 INF] Ứng dụng Chrome Auto Manager đã khởi động
[2025-05-28 13:11:41.395 +07:00 INF] Đã load 112 proxy từ file
[2025-05-28 13:12:41.618 +07:00 ERR] [BROWSER] Connect to existing browser - FAILED: The HTTP request to the remote WebDriver server for URL http://localhost:64017/session timed out after 60 seconds.
[2025-05-28 13:12:41.618 +07:00 INF] Không thể kết nối browser hiện có, tạo browser mới...
[2025-05-28 13:12:42.760 +07:00 INF] [BROWSER] Create new browser - SUCCESS
[2025-05-28 13:12:43.397 +07:00 INF] [BROWSER] Navigate - SUCCESS: https://www.13win16.com/home/<USER>
[2025-05-28 13:12:58.458 +07:00 WRN] Không tìm thấy element: input[name="username"]
[2025-05-28 13:13:08.504 +07:00 WRN] Không tìm thấy element: input[name="user"]
[2025-05-28 13:14:12.529 +07:00 INF] Logger initialized successfully
[2025-05-28 13:14:12.553 +07:00 INF] Ứng dụng Chrome Auto Manager đã khởi động
[2025-05-28 13:14:30.950 +07:00 INF] Đã load 112 proxy từ file
[2025-05-28 13:15:31.190 +07:00 ERR] [BROWSER] Connect to existing browser - FAILED: The HTTP request to the remote WebDriver server for URL http://localhost:64335/session timed out after 60 seconds.
[2025-05-28 13:15:31.190 +07:00 INF] Không thể kết nối browser hiện có, tạo browser mới...
[2025-05-28 13:15:32.393 +07:00 INF] [BROWSER] Create new browser - SUCCESS
[2025-05-28 13:15:33.184 +07:00 INF] [BROWSER] Navigate - SUCCESS: https://www.13win16.com/home/<USER>
[2025-05-28 13:15:48.261 +07:00 WRN] Không tìm thấy element: input[name="username"]
[2025-05-28 13:15:58.320 +07:00 WRN] Không tìm thấy element: input[name="user"]
[2025-05-28 13:16:08.348 +07:00 WRN] Không tìm thấy element: input[name="account"]
[2025-05-28 13:16:18.379 +07:00 WRN] Không tìm thấy element: input[placeholder*="用户"]
[2025-05-28 13:16:28.446 +07:00 WRN] Không tìm thấy element: input[placeholder*="账号"]
[2025-05-28 13:16:38.518 +07:00 WRN] Không tìm thấy element: input[placeholder*="username"]
[2025-05-28 13:16:48.577 +07:00 WRN] Không tìm thấy element: input[id*="username"]
[2025-05-28 13:16:58.599 +07:00 WRN] Không tìm thấy element: input[id*="user"]
[2025-05-28 13:17:08.665 +07:00 WRN] Không tìm thấy element: input[name="password"]
[2025-05-28 13:17:18.705 +07:00 WRN] Không tìm thấy element: input[name="pwd"]
[2025-05-28 13:17:28.731 +07:00 WRN] Không tìm thấy element: input[type="password"]
[2025-05-28 13:17:38.800 +07:00 WRN] Không tìm thấy element: input[placeholder*="密码"]
[2025-05-28 13:17:47.527 +07:00 ERR] Lỗi khi chờ element: input[placeholder*="password"]
OpenQA.Selenium.WebDriverException: An unknown exception was encountered sending an HTTP request to the remote WebDriver server for URL http://localhost:64434/session/b28ce985259b1d2f8f188cbc1a73b3e9/element. The exception message was: An error occurred while sending the request.
 ---> System.Net.Http.HttpRequestException: An error occurred while sending the request.
 ---> System.IO.IOException: Unable to read data from the transport connection: An existing connection was forcibly closed by the remote host..
 ---> System.Net.Sockets.SocketException (10054): An existing connection was forcibly closed by the remote host.
   --- End of inner exception stack trace ---
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource<System.Int32>.GetResult(Int16 token)
   at System.Net.Http.HttpConnection.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnection.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.MakeHttpRequest(HttpRequestInfo requestInfo)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.<>c__DisplayClass32_0.<<Execute>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   --- End of inner exception stack trace ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.Remote.DriverServiceCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.WebDriver.Execute(String driverCommandToExecute, Dictionary`2 parameters)
   at OpenQA.Selenium.WebDriver.FindElement(String mechanism, String value)
   at OpenQA.Selenium.By.<.ctor>b__11_0(ISearchContext context)
   at OpenQA.Selenium.By.FindElement(ISearchContext context)
   at OpenQA.Selenium.WebDriver.FindElement(By by)
   at ChromeAutoManager.Services.BrowserManager.<>c__DisplayClass8_0.<WaitForElement>b__0(IWebDriver driver) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition, CancellationToken token)
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition)
   at ChromeAutoManager.Services.BrowserManager.WaitForElement(String selector, By by, Int32 timeout) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
[2025-05-28 13:17:51.733 +07:00 ERR] Lỗi khi chờ element: input[id*="password"]
OpenQA.Selenium.WebDriverException: An unknown exception was encountered sending an HTTP request to the remote WebDriver server for URL http://localhost:64434/session/b28ce985259b1d2f8f188cbc1a73b3e9/element. The exception message was: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.MakeHttpRequest(HttpRequestInfo requestInfo)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.<>c__DisplayClass32_0.<<Execute>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   --- End of inner exception stack trace ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.Remote.DriverServiceCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.WebDriver.Execute(String driverCommandToExecute, Dictionary`2 parameters)
   at OpenQA.Selenium.WebDriver.FindElement(String mechanism, String value)
   at OpenQA.Selenium.By.<.ctor>b__11_0(ISearchContext context)
   at OpenQA.Selenium.By.FindElement(ISearchContext context)
   at OpenQA.Selenium.WebDriver.FindElement(By by)
   at ChromeAutoManager.Services.BrowserManager.<>c__DisplayClass8_0.<WaitForElement>b__0(IWebDriver driver) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition, CancellationToken token)
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition)
   at ChromeAutoManager.Services.BrowserManager.WaitForElement(String selector, By by, Int32 timeout) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
[2025-05-28 13:17:55.859 +07:00 ERR] Lỗi khi chờ element: input[id*="pwd"]
OpenQA.Selenium.WebDriverException: An unknown exception was encountered sending an HTTP request to the remote WebDriver server for URL http://localhost:64434/session/b28ce985259b1d2f8f188cbc1a73b3e9/element. The exception message was: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.MakeHttpRequest(HttpRequestInfo requestInfo)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.<>c__DisplayClass32_0.<<Execute>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   --- End of inner exception stack trace ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.Remote.DriverServiceCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.WebDriver.Execute(String driverCommandToExecute, Dictionary`2 parameters)
   at OpenQA.Selenium.WebDriver.FindElement(String mechanism, String value)
   at OpenQA.Selenium.By.<.ctor>b__11_0(ISearchContext context)
   at OpenQA.Selenium.By.FindElement(ISearchContext context)
   at OpenQA.Selenium.WebDriver.FindElement(By by)
   at ChromeAutoManager.Services.BrowserManager.<>c__DisplayClass8_0.<WaitForElement>b__0(IWebDriver driver) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition, CancellationToken token)
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition)
   at ChromeAutoManager.Services.BrowserManager.WaitForElement(String selector, By by, Int32 timeout) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
[2025-05-28 13:17:59.958 +07:00 ERR] Lỗi khi chờ element: input[name="confirmPassword"]
OpenQA.Selenium.WebDriverException: An unknown exception was encountered sending an HTTP request to the remote WebDriver server for URL http://localhost:64434/session/b28ce985259b1d2f8f188cbc1a73b3e9/element. The exception message was: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.MakeHttpRequest(HttpRequestInfo requestInfo)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.<>c__DisplayClass32_0.<<Execute>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   --- End of inner exception stack trace ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.Remote.DriverServiceCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.WebDriver.Execute(String driverCommandToExecute, Dictionary`2 parameters)
   at OpenQA.Selenium.WebDriver.FindElement(String mechanism, String value)
   at OpenQA.Selenium.By.<.ctor>b__11_0(ISearchContext context)
   at OpenQA.Selenium.By.FindElement(ISearchContext context)
   at OpenQA.Selenium.WebDriver.FindElement(By by)
   at ChromeAutoManager.Services.BrowserManager.<>c__DisplayClass8_0.<WaitForElement>b__0(IWebDriver driver) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition, CancellationToken token)
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition)
   at ChromeAutoManager.Services.BrowserManager.WaitForElement(String selector, By by, Int32 timeout) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
[2025-05-28 13:18:04.035 +07:00 ERR] Lỗi khi chờ element: input[name="confirm_password"]
OpenQA.Selenium.WebDriverException: An unknown exception was encountered sending an HTTP request to the remote WebDriver server for URL http://localhost:64434/session/b28ce985259b1d2f8f188cbc1a73b3e9/element. The exception message was: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.MakeHttpRequest(HttpRequestInfo requestInfo)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.<>c__DisplayClass32_0.<<Execute>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   --- End of inner exception stack trace ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.Remote.DriverServiceCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.WebDriver.Execute(String driverCommandToExecute, Dictionary`2 parameters)
   at OpenQA.Selenium.WebDriver.FindElement(String mechanism, String value)
   at OpenQA.Selenium.By.<.ctor>b__11_0(ISearchContext context)
   at OpenQA.Selenium.By.FindElement(ISearchContext context)
   at OpenQA.Selenium.WebDriver.FindElement(By by)
   at ChromeAutoManager.Services.BrowserManager.<>c__DisplayClass8_0.<WaitForElement>b__0(IWebDriver driver) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition, CancellationToken token)
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition)
   at ChromeAutoManager.Services.BrowserManager.WaitForElement(String selector, By by, Int32 timeout) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
[2025-05-28 13:18:08.147 +07:00 ERR] Lỗi khi chờ element: input[name="repassword"]
OpenQA.Selenium.WebDriverException: An unknown exception was encountered sending an HTTP request to the remote WebDriver server for URL http://localhost:64434/session/b28ce985259b1d2f8f188cbc1a73b3e9/element. The exception message was: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.MakeHttpRequest(HttpRequestInfo requestInfo)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.<>c__DisplayClass32_0.<<Execute>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   --- End of inner exception stack trace ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.Remote.DriverServiceCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.WebDriver.Execute(String driverCommandToExecute, Dictionary`2 parameters)
   at OpenQA.Selenium.WebDriver.FindElement(String mechanism, String value)
   at OpenQA.Selenium.By.<.ctor>b__11_0(ISearchContext context)
   at OpenQA.Selenium.By.FindElement(ISearchContext context)
   at OpenQA.Selenium.WebDriver.FindElement(By by)
   at ChromeAutoManager.Services.BrowserManager.<>c__DisplayClass8_0.<WaitForElement>b__0(IWebDriver driver) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition, CancellationToken token)
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition)
   at ChromeAutoManager.Services.BrowserManager.WaitForElement(String selector, By by, Int32 timeout) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
[2025-05-28 13:18:12.263 +07:00 ERR] Lỗi khi chờ element: input[placeholder*="确认密码"]
OpenQA.Selenium.WebDriverException: An unknown exception was encountered sending an HTTP request to the remote WebDriver server for URL http://localhost:64434/session/b28ce985259b1d2f8f188cbc1a73b3e9/element. The exception message was: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:64434)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.MakeHttpRequest(HttpRequestInfo requestInfo)
   at OpenQA.Selenium.Remote.HttpCommandExecutor.<>c__DisplayClass32_0.<<Execute>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   --- End of inner exception stack trace ---
   at OpenQA.Selenium.Remote.HttpCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.Remote.DriverServiceCommandExecutor.Execute(Command commandToExecute)
   at OpenQA.Selenium.WebDriver.Execute(String driverCommandToExecute, Dictionary`2 parameters)
   at OpenQA.Selenium.WebDriver.FindElement(String mechanism, String value)
   at OpenQA.Selenium.By.<.ctor>b__11_0(ISearchContext context)
   at OpenQA.Selenium.By.FindElement(ISearchContext context)
   at OpenQA.Selenium.WebDriver.FindElement(By by)
   at ChromeAutoManager.Services.BrowserManager.<>c__DisplayClass8_0.<WaitForElement>b__0(IWebDriver driver) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition, CancellationToken token)
   at OpenQA.Selenium.Support.UI.DefaultWait`1.Until[TResult](Func`2 condition)
   at ChromeAutoManager.Services.BrowserManager.WaitForElement(String selector, By by, Int32 timeout) in D:\chorme auto c#\chorme auto\Services\BrowserManager.cs:line 182
[2025-05-28 13:18:45.430 +07:00 INF] Logger initialized successfully
[2025-05-28 13:18:45.454 +07:00 INF] Ứng dụng Chrome Auto Manager đã khởi động
[2025-05-28 13:18:58.512 +07:00 INF] Đã load 112 proxy từ file
[2025-05-28 13:19:58.778 +07:00 ERR] [BROWSER] Connect to existing browser - FAILED: The HTTP request to the remote WebDriver server for URL http://localhost:64736/session timed out after 60 seconds.
[2025-05-28 13:19:58.779 +07:00 INF] Không thể kết nối browser hiện có, tạo browser mới...
[2025-05-28 13:20:00.283 +07:00 INF] [BROWSER] Create new browser - SUCCESS
[2025-05-28 13:20:01.144 +07:00 INF] [BROWSER] Navigate - SUCCESS: https://www.13win16.com/home/<USER>
[2025-05-28 13:24:17.127 +07:00 INF] Logger initialized successfully
[2025-05-28 13:24:17.150 +07:00 INF] Ứng dụng Chrome Auto Manager đã khởi động
[2025-05-28 13:24:22.482 +07:00 INF] Đã load 112 proxy từ file
[2025-05-28 13:25:22.785 +07:00 ERR] [BROWSER] Connect to existing browser - FAILED: The HTTP request to the remote WebDriver server for URL http://localhost:65150/session timed out after 60 seconds.
[2025-05-28 13:25:22.785 +07:00 INF] Không thể kết nối browser hiện có, tạo browser mới...
[2025-05-28 13:25:23.884 +07:00 INF] [BROWSER] Create new browser - SUCCESS
[2025-05-28 13:25:24.529 +07:00 INF] [BROWSER] Navigate - SUCCESS: https://www.13win16.com/home/<USER>
[2025-05-28 13:25:39.612 +07:00 WRN] Không tìm thấy element: input[name="username"]
[2025-05-28 13:25:49.691 +07:00 WRN] Không tìm thấy element: input[name="user"]
[2025-05-28 13:25:59.741 +07:00 WRN] Không tìm thấy element: input[name="account"]
[2025-05-28 13:26:09.780 +07:00 WRN] Không tìm thấy element: input[placeholder*="用户"]
[2025-05-28 13:26:19.794 +07:00 WRN] Không tìm thấy element: input[placeholder*="账号"]
[2025-05-28 13:26:29.800 +07:00 WRN] Không tìm thấy element: input[placeholder*="username"]
[2025-05-28 13:26:39.813 +07:00 WRN] Không tìm thấy element: input[id*="username"]
[2025-05-28 13:26:49.867 +07:00 WRN] Không tìm thấy element: input[id*="user"]
[2025-05-28 13:26:59.892 +07:00 WRN] Không tìm thấy element: input[name="password"]
[2025-05-28 13:27:09.952 +07:00 WRN] Không tìm thấy element: input[name="pwd"]
[2025-05-28 13:27:20.013 +07:00 WRN] Không tìm thấy element: input[type="password"]
[2025-05-28 13:27:30.055 +07:00 WRN] Không tìm thấy element: input[placeholder*="密码"]
[2025-05-28 13:27:40.067 +07:00 WRN] Không tìm thấy element: input[placeholder*="password"]
