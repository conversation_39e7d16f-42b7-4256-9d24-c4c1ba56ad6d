{"RegisterUrl": "https://www.13win16.com/home/<USER>", "Browser": {"Headless": false, "WindowWidth": 1366, "WindowHeight": 768, "UserAgentRotation": true, "DisableImages": false, "DisableCss": false, "PageLoadTimeout": 30, "ImplicitWait": 10, "UseExistingBrowser": true, "DebugPort": 9222}, "Proxy": {"UseProxy": true, "ProxySources": ["https://www.proxy-list.download/api/v1/get?type=http", "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt", "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt"], "ProxyTimeout": 10, "MaxProxyTestThreads": 50}, "Registration": {"MaxConcurrentBrowsers": 5, "DelayBetweenActionsMin": 1, "DelayBetweenActionsMax": 3, "RetryAttempts": 3, "SuccessDelayMin": 5, "SuccessDelayMax": 10}, "Account": {"EmailDomains": ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "protonmail.com", "tempmail.org", "10minutemail.com"], "PasswordLength": 12, "PhoneCountryCodes": ["+84", "+1", "+44", "+86", "+91"], "MinAge": 18, "MaxAge": 65}, "Output": {"SuccessFile": "successful_accounts.txt", "FailedFile": "failed_accounts.txt", "ProxyFile": "working_proxies.txt", "LogFile": "registration.log", "ScreenshotFolder": "screenshots"}, "FormSelectors": {"UsernamePhone": "input[placeholder*=\"<PERSON><PERSON><PERSON><PERSON> đ<PERSON> tho<PERSON>/<PERSON>ê<PERSON>\"], input[name=\"username\"], input[name=\"phone\"]", "Password": "input[placeholder*=\"<PERSON>ật kh<PERSON>u\"], input[name=\"password\"], input[type=\"password\"]", "ConfirmPassword": "input[placeholder*=\"<PERSON><PERSON> lòng xác nhận lại mật khẩu\"], input[name=\"confirmPassword\"], input[name=\"confirm_password\"]", "RealName": "input[placeholder*=\"<PERSON><PERSON>\"], input[name=\"realName\"], input[name=\"fullName\"]", "SubmitButton": "button:contains(\"ĐĂNG KÝ\"), button[type=\"submit\"], input[type=\"submit\"], .submit-btn", "TermsCheckbox": "input[type=\"checkbox\"]", "Captcha": "input[name=\"captcha\"], input[name=\"code\"], input[placeholder*=\"验证码\"]"}, "Delay": {"PageLoadWait": 5, "ElementWait": 10, "AfterInputMin": 0.5, "AfterInputMax": 1.5, "AfterClickMin": 1, "AfterClickMax": 2, "AfterSubmit": 5, "RetryDelay": 3}}