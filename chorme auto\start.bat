@echo off
title Chrome Auto Manager

echo ================================================================
echo                    CHROME AUTO MANAGER
echo                      C# GUI VERSION
echo ================================================================
echo.

if exist "bin\Release\net6.0-windows\ChromeAutoManager.exe" (
    echo [OK] Starting application...
    start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    echo [OK] Application started successfully!
) else (
    echo [INFO] Executable not found. Building application...
    dotnet build ChromeAutoManager.csproj --configuration Release
    if %errorlevel% equ 0 (
        echo [OK] Build successful! Starting application...
        start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    ) else (
        echo [ERROR] Build failed! Please check .NET installation.
        pause
    )
)

echo.
echo [INFO] Application is running in background.
echo [INFO] Check the GUI window for the interface.
echo.
pause
