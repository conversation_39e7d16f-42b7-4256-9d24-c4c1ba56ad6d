# 🛠️ Development Guide - Chrome Auto Manager

Hướng dẫn phát triển và maintain Chrome Auto Manager với cấu trúc code đã được tối ưu.

## 🏗️ Cấu trúc project đã được tối ưu

### ✅ Đã dọn dẹp
- ✅ **Python code** → Moved to `archive/python-original/`
- ✅ **Build outputs** → Hidden và ignored
- ✅ **Temporary files** → Cleaned up
- ✅ **Documentation** → Organized in `docs/`
- ✅ **Scripts** → Organized in `scripts/`

### ✅ Đã cấu hình VS Code
- ✅ **IntelliSense** cho C#
- ✅ **Debug configuration**
- ✅ **Build tasks**
- ✅ **Code formatting**
- ✅ **File nesting**
- ✅ **Extensions recommendations**

## 🚀 Development Workflow

### 1. Setup môi trường
```bash
# Clone/download project
git clone <repo-url>
cd "chorme auto"

# Mở trong VS Code
code .
# Hoặc mở workspace file
code ChromeAutoManager.code-workspace
```

### 2. Cài đặt extensions
VS Code sẽ tự động đề xuất:
- **C# for Visual Studio Code**
- **.NET Install Tool**
- **JSON Language Features**
- **PowerShell**

### 3. Build & Run
```bash
# Build (Ctrl+Shift+B)
dotnet build

# Run Debug (F5)
dotnet run

# Run Release (Ctrl+F5)
dotnet run --configuration Release
```

## 📁 Code Organization

### Core Layer
```
Core/
├── AppConfig.cs      # Configuration management
└── Logger.cs         # Logging với Serilog
```

### Models Layer
```
Models/
├── Account.cs              # Account data model
├── ProxyInfo.cs           # Proxy information
└── RegistrationResult.cs  # Registration results
```

### Services Layer
```
Services/
├── AccountGenerator.cs    # Generate account info
├── BrowserManager.cs      # Selenium WebDriver management
├── ProxyManager.cs        # Proxy fetching & testing
└── RegistrationBot.cs     # Registration automation
```

### Forms Layer
```
Forms/
├── MainForm.cs       # Main GUI application
└── SettingsForm.cs   # Settings configuration
```

## 🔧 Development Tools

### VS Code Tasks
- **build** - Build project
- **build-release** - Build release version
- **clean** - Clean build outputs
- **restore** - Restore NuGet packages
- **publish** - Publish for deployment
- **watch** - Watch for changes

### Debug Configuration
- **Launch Chrome Auto Manager** - Debug mode
- **Launch Chrome Auto Manager (Release)** - Release mode

### Code Formatting
- **EditorConfig** - Consistent formatting rules
- **Format on Save** - Auto-format khi save
- **Organize Imports** - Auto-organize using statements

## 📝 Coding Standards

### Naming Conventions
```csharp
// Classes: PascalCase
public class AccountGenerator

// Methods: PascalCase
public void GenerateAccount()

// Properties: PascalCase
public string Username { get; set; }

// Fields: camelCase with underscore
private readonly ProxyManager _proxyManager;

// Constants: PascalCase
public const int MaxRetryAttempts = 3;
```

### Code Structure
```csharp
// File header
using System;
using System.Threading.Tasks;

namespace ChromeAutoManager.Services
{
    /// <summary>
    /// Service description
    /// </summary>
    public class ServiceName
    {
        // Fields
        private readonly DependencyType _dependency;
        
        // Constructor
        public ServiceName(DependencyType dependency)
        {
            _dependency = dependency;
        }
        
        // Properties
        public string PropertyName { get; set; }
        
        // Methods
        public async Task<ResultType> MethodNameAsync()
        {
            // Implementation
        }
    }
}
```

### Error Handling
```csharp
try
{
    // Risky operation
    var result = await SomeOperationAsync();
    Logger.Info("Operation successful: {Result}", result);
    return result;
}
catch (SpecificException ex)
{
    Logger.Error(ex, "Specific error occurred: {Message}", ex.Message);
    throw;
}
catch (Exception ex)
{
    Logger.Fatal(ex, "Unexpected error in {Method}", nameof(MethodName));
    throw;
}
```

## 🧪 Testing

### Unit Testing Setup
```bash
# Add test project
dotnet new xunit -n ChromeAutoManager.Tests
dotnet add ChromeAutoManager.Tests reference ChromeAutoManager.csproj
```

### Test Structure
```csharp
[Fact]
public void AccountGenerator_GenerateUsername_ShouldReturnValidUsername()
{
    // Arrange
    var generator = new AccountGenerator();
    
    // Act
    var username = generator.GenerateUsername();
    
    // Assert
    Assert.NotNull(username);
    Assert.True(username.Length > 0);
}
```

## 📦 Package Management

### Adding Packages
```bash
# Add NuGet package
dotnet add package PackageName

# Add specific version
dotnet add package PackageName --version 1.2.3
```

### Current Dependencies
- **Selenium.WebDriver** - Browser automation
- **Bogus** - Fake data generation
- **Serilog** - Structured logging
- **Newtonsoft.Json** - JSON handling

## 🚀 Deployment

### Build for Release
```bash
# Build release
dotnet build --configuration Release

# Publish self-contained
dotnet publish --configuration Release --self-contained true --runtime win-x64
```

### Distribution
```bash
# Create distribution folder
mkdir dist
copy bin\Release\net6.0-windows\* dist\
copy start.bat dist\
copy README.md dist\
```

## 🔍 Debugging Tips

### Common Issues
1. **Selenium WebDriver errors**
   - Check Chrome version compatibility
   - Ensure ChromeDriver is in PATH
   - Verify debug port availability

2. **Proxy connection issues**
   - Test proxy connectivity
   - Check firewall settings
   - Verify proxy format

3. **Form detection failures**
   - Check website structure changes
   - Update selectors in AppConfig
   - Test with browser developer tools

### Debug Techniques
```csharp
// Use conditional compilation
#if DEBUG
    Logger.Debug("Debug info: {Value}", someValue);
#endif

// Use Debug.WriteLine for immediate output
System.Diagnostics.Debug.WriteLine($"Current state: {state}");

// Use breakpoints with conditions
// Right-click breakpoint > Edit Breakpoint > Add condition
```

## 📚 Documentation

### Code Documentation
```csharp
/// <summary>
/// Generates a random account with specified parameters
/// </summary>
/// <param name="includeEmail">Whether to include email generation</param>
/// <returns>A new Account instance with generated data</returns>
public Account GenerateAccount(bool includeEmail = true)
{
    // Implementation
}
```

### README Updates
- Update `README.md` for user-facing changes
- Update `docs/VS_CODE_GUIDE.md` for development changes
- Update `DEVELOPMENT_GUIDE.md` for architecture changes

## 🔄 Git Workflow

### Recommended Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

### Commit Messages
- **feat:** New feature
- **fix:** Bug fix
- **docs:** Documentation changes
- **style:** Code style changes
- **refactor:** Code refactoring
- **test:** Adding tests
- **chore:** Maintenance tasks

## 🎯 Performance Optimization

### Memory Management
```csharp
// Use using statements for disposables
using var browser = new BrowserManager();

// Dispose resources explicitly
public void Dispose()
{
    _browser?.Dispose();
    _httpClient?.Dispose();
}
```

### Async Best Practices
```csharp
// Use ConfigureAwait(false) in libraries
await SomeOperationAsync().ConfigureAwait(false);

// Use Task.WhenAll for parallel operations
var tasks = accounts.Select(ProcessAccountAsync);
await Task.WhenAll(tasks);
```

## 🛡️ Security Considerations

### Sensitive Data
- Never commit passwords or API keys
- Use configuration files for sensitive settings
- Implement secure storage for credentials

### Input Validation
```csharp
public bool ValidateAccount(Account account)
{
    if (string.IsNullOrWhiteSpace(account.Username))
        throw new ArgumentException("Username cannot be empty");
        
    if (account.Password.Length < 6)
        throw new ArgumentException("Password too short");
        
    return true;
}
```

---

**Happy Development!** 🚀

Với cấu trúc đã được tối ưu này, việc phát triển và maintain Chrome Auto Manager sẽ trở nên dễ dàng và hiệu quả hơn.
