using System;

namespace ChromeAutoManager.Models
{
    /// <summary>
    /// Model đại diện cho thông tin proxy
    /// </summary>
    public class ProxyInfo
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public ProxyType Type { get; set; } = ProxyType.Http;
        public bool IsWorking { get; set; } = false;
        public DateTime LastTested { get; set; } = DateTime.MinValue;
        public int ResponseTime { get; set; } = 0; // milliseconds
        public string Country { get; set; } = string.Empty;
        public bool InUse { get; set; } = false;

        /// <summary>
        /// Đ<PERSON>a chỉ proxy đầy đủ
        /// </summary>
        public string FullAddress => $"{Host}:{Port}";

        /// <summary>
        /// Địa chỉ proxy với authentication
        /// </summary>
        public string AuthenticatedAddress
        {
            get
            {
                if (!string.IsNullOrEmpty(Username) && !string.IsNullOrEmpty(Password))
                {
                    return $"{Username}:{Password}@{Host}:{Port}";
                }
                return FullAddress;
            }
        }

        public override string ToString()
        {
            var status = IsWorking ? "✓" : "✗";
            var inUseStatus = InUse ? " (Đang sử dụng)" : "";
            return $"{status} {FullAddress} - {ResponseTime}ms{inUseStatus}";
        }

        /// <summary>
        /// Kiểm tra proxy có hợp lệ không
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Host) && Port > 0 && Port <= 65535;
        }

        /// <summary>
        /// Tạo bản sao của proxy
        /// </summary>
        public ProxyInfo Clone()
        {
            return new ProxyInfo
            {
                Host = this.Host,
                Port = this.Port,
                Username = this.Username,
                Password = this.Password,
                Type = this.Type,
                IsWorking = this.IsWorking,
                LastTested = this.LastTested,
                ResponseTime = this.ResponseTime,
                Country = this.Country,
                InUse = this.InUse
            };
        }
    }

    /// <summary>
    /// Loại proxy
    /// </summary>
    public enum ProxyType
    {
        Http,
        Https,
        Socks4,
        Socks5
    }
}
