using System;
using System.IO;
using System.Windows.Forms;
using ChromeAutoManager.Core;
using ChromeAutoManager.Forms;

namespace ChromeAutoManager
{
    /// <summary>
    /// Entry point của ứng dụng
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // Cấu hình ứng dụng
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.SetHighDpiMode(HighDpiMode.SystemAware);

                // Tạo thư mục cần thiết
                CreateRequiredDirectories();

                // Khởi tạo logger
                Logger.Info("Ứng dụng Chrome Auto Manager đã khởi động");

                // Hiển thị form chính
                using var mainForm = new MainForm();
                Application.Run(mainForm);

                Logger.Info("Ứng dụng đã thoát");
            }
            catch (Exception ex)
            {
                var errorMessage = $"Lỗi khởi động ứng dụng: {ex.Message}\n\nChi tiết:\n{ex}";
                MessageBox.Show(errorMessage, "Lỗi Khởi Động", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Logger.Fatal(ex, "Lỗi khởi động ứng dụng");
            }
            finally
            {
                Logger.CloseAndFlush();
            }
        }

        /// <summary>
        /// Tạo các thư mục cần thiết
        /// </summary>
        private static void CreateRequiredDirectories()
        {
            try
            {
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                
                var directories = new[]
                {
                    "logs",
                    "screenshots", 
                    "output",
                    "chrome_profiles",
                    "drivers"
                };

                foreach (var directory in directories)
                {
                    var fullPath = Path.Combine(baseDirectory, directory);
                    if (!Directory.Exists(fullPath))
                    {
                        Directory.CreateDirectory(fullPath);
                        Logger.Info("Đã tạo thư mục: {Directory}", fullPath);
                    }
                }

                // Tạo file cấu hình mặc định nếu chưa có
                var configPath = Path.Combine(baseDirectory, "config.json");
                if (!File.Exists(configPath))
                {
                    AppConfig.Instance.SaveConfig();
                    Logger.Info("Đã tạo file cấu hình mặc định");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi tạo thư mục");
            }
        }
    }
}
