@echo off
title Chrome Auto Manager - Quick Test
color 0E

echo ================================================================
echo                    CHROME AUTO MANAGER
echo                      QUICK TEST
echo ================================================================
echo.

echo [TEST] Killing any existing processes...
taskkill /f /im "ChromeAutoManager.exe" >nul 2>&1

echo [TEST] Building application...
dotnet build ChromeAutoManager.csproj --configuration Debug --verbosity quiet
if %errorlevel% neq 0 (
    echo [ERROR] Build failed!
    pause
    exit /b 1
)

echo [TEST] Starting application...
echo [INFO] If GUI doesn't appear in 5 seconds, there's an issue.
echo.

start "" "bin\Debug\net6.0-windows\ChromeAutoManager.exe"

echo [TEST] Waiting 3 seconds...
timeout /t 3 /nobreak >nul

echo [TEST] Checking if process is running...
tasklist | findstr "ChromeAutoManager.exe" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] Process is running!
    echo [INFO] Check if GUI window is visible.
    echo [INFO] If not visible, try Alt+Tab or check taskbar.
) else (
    echo [ERROR] Process not found! Application may have crashed.
)

echo.
echo [TEST] Press any key to kill the process and exit...
pause >nul

echo [TEST] Cleaning up...
taskkill /f /im "ChromeAutoManager.exe" >nul 2>&1
echo [TEST] Done.
