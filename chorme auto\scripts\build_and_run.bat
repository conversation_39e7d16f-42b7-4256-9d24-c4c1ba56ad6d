@echo off
title Chrome Auto Manager - Build and Run

echo.
echo ================================================================
echo                    CHROME AUTO MANAGER
echo                      C# GUI VERSION
echo.
echo   Tool tu dong dang ky tai khoan voi giao dien do hoa
echo   Chuyen doi tu Python sang C# WinForms
echo   Theo doi ket qua real-time
echo ================================================================
echo.

:start
echo Lua chon:
echo 1. Build va chay ung dung
echo 2. Chi build ung dung
echo 3. Chay ung dung (da build)
echo 4. Restore packages
echo 5. Clean build
echo 6. Thoat
echo.

set /p choice="Nhap lua chon (1-6): "

if "%choice%"=="1" goto build_and_run
if "%choice%"=="2" goto build_only
if "%choice%"=="3" goto run_only
if "%choice%"=="4" goto restore
if "%choice%"=="5" goto clean
if "%choice%"=="6" goto exit

echo Lua chon khong hop le!
pause
goto start

:restore
echo.
echo [INFO] Dang restore packages...
dotnet restore ChromeAutoManager.csproj
if %errorlevel% neq 0 (
    echo [ERROR] Loi khi restore packages!
    pause
    goto start
)
echo [OK] Restore packages thanh cong!
pause
goto start

:clean
echo.
echo [INFO] Dang clean build...
dotnet clean ChromeAutoManager.csproj
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
echo [OK] Clean thanh cong!
pause
goto start

:build_only
echo.
echo [INFO] Dang build ung dung...
dotnet build ChromeAutoManager.csproj --configuration Release
if %errorlevel% neq 0 (
    echo [ERROR] Build that bai!
    pause
    goto start
)
echo [OK] Build thanh cong!
pause
goto start

:run_only
echo.
echo [INFO] Dang chay ung dung...
if exist "bin\Release\net6.0-windows\ChromeAutoManager.exe" (
    start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    echo [OK] Ung dung da duoc khoi dong!
) else (
    echo [ERROR] Khong tim thay file exe! Vui long build truoc.
    echo [INFO] Dang thu chay voi dotnet run...
    dotnet run --project ChromeAutoManager.csproj
)
pause
goto start

:build_and_run
echo.
echo [INFO] Dang restore packages...
dotnet restore ChromeAutoManager.csproj
if %errorlevel% neq 0 (
    echo [ERROR] Loi khi restore packages!
    pause
    goto start
)

echo.
echo [INFO] Dang build ung dung...
dotnet build ChromeAutoManager.csproj --configuration Release
if %errorlevel% neq 0 (
    echo [ERROR] Build that bai!
    pause
    goto start
)

echo.
echo [INFO] Dang chay ung dung...
if exist "bin\Release\net6.0-windows\ChromeAutoManager.exe" (
    start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    echo [OK] Ung dung da duoc khoi dong thanh cong!
) else (
    echo [WARNING] Khong tim thay file exe, chay voi dotnet run...
    dotnet run --project ChromeAutoManager.csproj
)
pause
goto start

:exit
echo.
echo [BYE] Cam on ban da su dung Chrome Auto Manager!
echo.
pause
exit

:error
echo.
echo [ERROR] Da xay ra loi! Vui long kiem tra:
echo   - .NET 6.0 SDK da duoc cai dat
echo   - Ket noi internet de download packages
echo   - Quyen ghi file trong thu muc hien tai
echo.
pause
goto start
