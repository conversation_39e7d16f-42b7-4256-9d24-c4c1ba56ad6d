@echo off
title Chrome Auto Manager - Form Analysis Debug
color 0B

echo ================================================================
echo                    FORM ANALYSIS DEBUG
echo                  Chrome Auto Manager
echo ================================================================
echo.

echo [DEBUG] Checking if application is running...
tasklist | findstr "ChromeAutoManager.exe" >nul
if %errorlevel% equ 0 (
    echo [INFO] Application is running. Good!
) else (
    echo [ERROR] Application not running. Please start it first.
    pause
    exit /b 1
)

echo.
echo [DEBUG] Checking for analysis output files...
echo.

if exist "page_source.html" (
    echo [FOUND] page_source.html - Form HTML source
    echo [INFO] Size: 
    for %%A in (page_source.html) do echo         %%~zA bytes
) else (
    echo [MISSING] page_source.html - Form analysis not run yet
)

if exist "registration_form.png" (
    echo [FOUND] registration_form.png - Form screenshot
    echo [INFO] Size: 
    for %%A in (registration_form.png) do echo         %%~zA bytes
) else (
    echo [MISSING] registration_form.png - Form analysis not run yet
)

echo.
echo [DEBUG] Checking Chrome debug connection...
netstat -an | findstr ":9222" >nul
if %errorlevel% equ 0 (
    echo [FOUND] Chrome debug port 9222 is open
) else (
    echo [MISSING] Chrome debug port 9222 not found
    echo [INFO] Chrome may not be running in debug mode
)

echo.
echo [DEBUG] Checking logs folder...
if exist "logs" (
    echo [FOUND] Logs folder exists
    dir logs\*.log /b 2>nul | findstr . >nul
    if %errorlevel% equ 0 (
        echo [INFO] Log files found:
        dir logs\*.log /b
    ) else (
        echo [INFO] No log files yet
    )
) else (
    echo [MISSING] Logs folder not found
)

echo.
echo ================================================================
echo                    ANALYSIS INSTRUCTIONS
echo ================================================================
echo.
echo 1. Make sure Chrome Auto Manager is running
echo 2. Click "🔍 Phân Tích Form" button in the app
echo 3. Wait for analysis to complete (10-30 seconds)
echo 4. Check the files created:
echo    - page_source.html: Contains the form HTML
echo    - registration_form.png: Screenshot of the form
echo.
echo 5. If analysis hangs:
echo    - Check if Chrome opens the registration URL
echo    - Look for any popup blockers or security warnings
echo    - Check if the page loads completely
echo.
echo 6. Common issues:
echo    - URL redirects to login page instead of registration
echo    - Page requires manual captcha solving
echo    - JavaScript blocks automated access
echo    - Form fields have dynamic names/IDs
echo.
echo ================================================================
echo.

echo [ACTION] What would you like to do?
echo.
echo 1. Open page_source.html (if exists)
echo 2. Open registration_form.png (if exists)
echo 3. View latest log file
echo 4. Start Chrome in debug mode manually
echo 5. Exit
echo.
set /p choice="Enter choice (1-5): "

if "%choice%"=="1" (
    if exist "page_source.html" (
        echo [INFO] Opening page_source.html in default browser...
        start "" "page_source.html"
    ) else (
        echo [ERROR] page_source.html not found. Run form analysis first.
    )
)

if "%choice%"=="2" (
    if exist "registration_form.png" (
        echo [INFO] Opening registration_form.png...
        start "" "registration_form.png"
    ) else (
        echo [ERROR] registration_form.png not found. Run form analysis first.
    )
)

if "%choice%"=="3" (
    if exist "logs" (
        echo [INFO] Latest log files:
        for /f %%i in ('dir logs\*.log /b /o-d 2^>nul') do (
            echo [INFO] Opening logs\%%i
            start notepad "logs\%%i"
            goto :done
        )
        echo [ERROR] No log files found
        :done
    ) else (
        echo [ERROR] Logs folder not found
    )
)

if "%choice%"=="4" (
    echo [INFO] Starting Chrome in debug mode...
    echo [INFO] This will open Chrome with remote debugging enabled
    start "" "chrome.exe" --remote-debugging-port=9222 --user-data-dir="chrome_profiles\debug" --disable-web-security --disable-features=VizDisplayCompositor
    echo [INFO] Chrome started. You can now run form analysis.
)

echo.
echo [DEBUG] Debug session complete.
pause
