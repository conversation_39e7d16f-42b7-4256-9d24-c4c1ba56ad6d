@echo off
chcp 65001 >nul
title Chrome Auto Manager - Build and Run

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    CHROME AUTO MANAGER                       ║
echo ║                      C# GUI VERSION                          ║
echo ║                                                              ║
echo ║  🚀 Tool tự động đăng ký tài khoản với giao diện đồ họa     ║
echo ║  🌐 Chuyển đổi từ Python sang C# WinForms                   ║
echo ║  📊 Theo dõi kết quả real-time                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:start
echo Lựa chọn:
echo 1. Build và chạy ứng dụng
echo 2. Chỉ build ứng dụng
echo 3. Chạy ứng dụng (đã build)
echo 4. Restore packages
echo 5. Clean build
echo 6. Thoát
echo.

set /p choice="Nhập lựa chọn (1-6): "

if "%choice%"=="1" goto build_and_run
if "%choice%"=="2" goto build_only
if "%choice%"=="3" goto run_only
if "%choice%"=="4" goto restore
if "%choice%"=="5" goto clean
if "%choice%"=="6" goto exit

echo Lựa chọn không hợp lệ!
pause
goto start

:restore
echo.
echo 📦 Đang restore packages...
dotnet restore ChromeAutoManager.csproj
if %errorlevel% neq 0 (
    echo ❌ Lỗi khi restore packages!
    pause
    goto start
)
echo ✅ Restore packages thành công!
pause
goto start

:clean
echo.
echo 🧹 Đang clean build...
dotnet clean ChromeAutoManager.csproj
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
echo ✅ Clean thành công!
pause
goto start

:build_only
echo.
echo 🔨 Đang build ứng dụng...
dotnet build ChromeAutoManager.csproj --configuration Release
if %errorlevel% neq 0 (
    echo ❌ Build thất bại!
    pause
    goto start
)
echo ✅ Build thành công!
pause
goto start

:run_only
echo.
echo 🚀 Đang chạy ứng dụng...
if exist "bin\Release\net6.0-windows\ChromeAutoManager.exe" (
    start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    echo ✅ Ứng dụng đã được khởi động!
) else (
    echo ❌ Không tìm thấy file exe! Vui lòng build trước.
    echo Đang thử chạy với dotnet run...
    dotnet run --project ChromeAutoManager.csproj
)
pause
goto start

:build_and_run
echo.
echo 📦 Đang restore packages...
dotnet restore ChromeAutoManager.csproj
if %errorlevel% neq 0 (
    echo ❌ Lỗi khi restore packages!
    pause
    goto start
)

echo.
echo 🔨 Đang build ứng dụng...
dotnet build ChromeAutoManager.csproj --configuration Release
if %errorlevel% neq 0 (
    echo ❌ Build thất bại!
    pause
    goto start
)

echo.
echo 🚀 Đang chạy ứng dụng...
if exist "bin\Release\net6.0-windows\ChromeAutoManager.exe" (
    start "" "bin\Release\net6.0-windows\ChromeAutoManager.exe"
    echo ✅ Ứng dụng đã được khởi động thành công!
) else (
    echo ⚠️ Không tìm thấy file exe, chạy với dotnet run...
    dotnet run --project ChromeAutoManager.csproj
)
pause
goto start

:exit
echo.
echo 👋 Cảm ơn bạn đã sử dụng Chrome Auto Manager!
echo.
pause
exit

:error
echo.
echo ❌ Đã xảy ra lỗi! Vui lòng kiểm tra:
echo   - .NET 6.0 SDK đã được cài đặt
echo   - Kết nối internet để download packages
echo   - Quyền ghi file trong thư mục hiện tại
echo.
pause
goto start
