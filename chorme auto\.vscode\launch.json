{"version": "0.2.0", "configurations": [{"name": "Launch Chrome Auto Manager", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net6.0-windows/ChromeAutoManager.exe", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false}, {"name": "Launch Chrome Auto Manager (Release)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-release", "program": "${workspaceFolder}/bin/Release/net6.0-windows/ChromeAutoManager.exe", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false}]}