using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using OpenQA.Selenium;
using ChromeAutoManager.Models;
using ChromeAutoManager.Core;

namespace ChromeAutoManager.Services
{
    /// <summary>
    /// Service bot đăng ký tài khoản tự động
    /// </summary>
    public class RegistrationBot : IDisposable
    {
        private readonly BrowserManager _browserManager;
        private readonly AccountGenerator _accountGenerator;
        private readonly ProxyInfo? _proxy;
        private bool _disposed = false;

        public RegistrationBot(ProxyInfo? proxy = null)
        {
            _proxy = proxy;
            _browserManager = new BrowserManager(proxy);
            _accountGenerator = new AccountGenerator();
        }

        /// <summary>
        /// Phân tích form đăng ký để tìm selectors
        /// </summary>
        public async Task<Dictionary<string, string>?> AnalyzeFormAsync()
        {
            try
            {
                if (!_browserManager.CreateBrowser())
                {
                    return null;
                }

                if (!_browserManager.NavigateToUrl(AppConfig.Instance.RegisterUrl))
                {
                    return null;
                }

                // Chờ trang load
                await Task.Delay(5000);

                var selectors = new Dictionary<string, string>();

                // Các selector có thể có
                var possibleSelectors = new Dictionary<string, string[]>
                {
                    ["username"] = new[]
                    {
                        "input[name=\"username\"]",
                        "input[name=\"user\"]",
                        "input[name=\"account\"]",
                        "input[placeholder*=\"用户\"]",
                        "input[placeholder*=\"账号\"]",
                        "input[placeholder*=\"username\"]",
                        "input[id*=\"username\"]",
                        "input[id*=\"user\"]"
                    },
                    ["password"] = new[]
                    {
                        "input[name=\"password\"]",
                        "input[name=\"pwd\"]",
                        "input[type=\"password\"]",
                        "input[placeholder*=\"密码\"]",
                        "input[placeholder*=\"password\"]",
                        "input[id*=\"password\"]",
                        "input[id*=\"pwd\"]"
                    },
                    ["confirm_password"] = new[]
                    {
                        "input[name=\"confirmPassword\"]",
                        "input[name=\"confirm_password\"]",
                        "input[name=\"repassword\"]",
                        "input[placeholder*=\"确认密码\"]",
                        "input[placeholder*=\"confirm\"]",
                        "input[id*=\"confirm\"]"
                    },
                    ["email"] = new[]
                    {
                        "input[name=\"email\"]",
                        "input[type=\"email\"]",
                        "input[placeholder*=\"邮箱\"]",
                        "input[placeholder*=\"email\"]",
                        "input[id*=\"email\"]"
                    },
                    ["phone"] = new[]
                    {
                        "input[name=\"phone\"]",
                        "input[name=\"mobile\"]",
                        "input[name=\"tel\"]",
                        "input[placeholder*=\"手机\"]",
                        "input[placeholder*=\"电话\"]",
                        "input[placeholder*=\"phone\"]",
                        "input[id*=\"phone\"]",
                        "input[id*=\"mobile\"]"
                    },
                    ["real_name"] = new[]
                    {
                        "input[name=\"realName\"]",
                        "input[name=\"fullName\"]",
                        "input[name=\"name\"]",
                        "input[placeholder*=\"姓名\"]",
                        "input[placeholder*=\"真实姓名\"]",
                        "input[id*=\"name\"]"
                    },
                    ["captcha"] = new[]
                    {
                        "input[name=\"captcha\"]",
                        "input[name=\"code\"]",
                        "input[name=\"verifyCode\"]",
                        "input[placeholder*=\"验证码\"]",
                        "input[placeholder*=\"captcha\"]",
                        "input[id*=\"captcha\"]",
                        "input[id*=\"code\"]"
                    },
                    ["submit_button"] = new[]
                    {
                        "button[type=\"submit\"]",
                        "input[type=\"submit\"]",
                        "button:contains(\"注册\")",
                        "button:contains(\"提交\")",
                        "button:contains(\"Register\")",
                        "button:contains(\"Submit\")",
                        ".submit-btn",
                        ".register-btn"
                    },
                    ["terms_checkbox"] = new[]
                    {
                        "input[type=\"checkbox\"]",
                        "input[name*=\"agree\"]",
                        "input[name*=\"terms\"]",
                        "input[id*=\"agree\"]",
                        "input[id*=\"terms\"]"
                    }
                };

                // Tìm selectors
                foreach (var field in possibleSelectors)
                {
                    foreach (var selector in field.Value)
                    {
                        try
                        {
                            var element = _browserManager.WaitForElement(selector, timeout: 2);
                            if (element != null && element.Displayed)
                            {
                                selectors[field.Key] = selector;
                                Logger.Info("Tìm thấy {Field}: {Selector}", field.Key, selector);
                                break;
                            }
                        }
                        catch
                        {
                            // Ignore và thử selector tiếp theo
                        }
                    }
                }

                // Lưu page source để phân tích
                var pageSource = _browserManager.GetPageSource();
                if (!string.IsNullOrEmpty(pageSource))
                {
                    await File.WriteAllTextAsync("page_source.html", pageSource);
                }

                // Chụp screenshot
                _browserManager.TakeScreenshot("registration_form.png");

                Logger.Info("Tìm thấy selectors: {Selectors}", string.Join(", ", selectors.Keys));
                return selectors;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi phân tích form");
                return null;
            }
        }

        /// <summary>
        /// Đăng ký một tài khoản
        /// </summary>
        public async Task<RegistrationResult> RegisterAccountAsync(Dictionary<string, string> selectors, Account? manualAccount = null)
        {
            var result = new RegistrationResult();
            var startTime = DateTime.Now;

            try
            {
                // Tạo thông tin tài khoản
                if (manualAccount != null)
                {
                    result.Account = manualAccount;
                    Logger.Info("Sử dụng thông tin thủ công: {Username}", manualAccount.Username);
                }
                else
                {
                    result.Account = _accountGenerator.GenerateCompleteAccount();
                }

                // Tạo browser
                if (!_browserManager.CreateBrowser())
                {
                    result.ErrorMessage = "Không thể tạo trình duyệt";
                    return result;
                }

                result.LastStep = RegistrationStep.NavigatingToPage;

                // Điều hướng đến trang đăng ký
                if (!_browserManager.NavigateToUrl(AppConfig.Instance.RegisterUrl))
                {
                    result.ErrorMessage = "Không thể load trang đăng ký";
                    return result;
                }

                // Chờ trang load
                await Task.Delay(3000);

                result.LastStep = RegistrationStep.FillingUsername;

                // Điền form đăng ký
                var success = true;
                var errorMsg = "";

                // Username/Phone (trường đầu tiên)
                if (selectors.ContainsKey("username") && success)
                {
                    var element = _browserManager.WaitForElement(selectors["username"]);
                    if (element != null)
                    {
                        var usernameValue = result.Account.Username;
                        if (!await _browserManager.SafeSendKeysAsync(element, usernameValue))
                        {
                            success = false;
                            errorMsg = "Lỗi khi nhập username";
                        }
                    }
                }

                result.LastStep = RegistrationStep.FillingPassword;

                // Password
                if (selectors.ContainsKey("password") && success)
                {
                    var element = _browserManager.WaitForElement(selectors["password"]);
                    if (element != null)
                    {
                        if (!await _browserManager.SafeSendKeysAsync(element, result.Account.Password))
                        {
                            success = false;
                            errorMsg = "Lỗi khi nhập password";
                        }
                    }
                }

                result.LastStep = RegistrationStep.FillingConfirmPassword;

                // Confirm Password
                if (selectors.ContainsKey("confirm_password") && success)
                {
                    var element = _browserManager.WaitForElement(selectors["confirm_password"]);
                    if (element != null)
                    {
                        if (!await _browserManager.SafeSendKeysAsync(element, result.Account.Password))
                        {
                            success = false;
                            errorMsg = "Lỗi khi nhập confirm password";
                        }
                    }
                }

                result.LastStep = RegistrationStep.FillingRealName;

                // Real Name
                if (selectors.ContainsKey("real_name") && success)
                {
                    var element = _browserManager.WaitForElement(selectors["real_name"]);
                    if (element != null)
                    {
                        if (!await _browserManager.SafeSendKeysAsync(element, result.Account.FullName))
                        {
                            success = false;
                            errorMsg = "Lỗi khi nhập họ tên thật";
                        }
                    }
                }

                result.LastStep = RegistrationStep.AcceptingTerms;

                // Terms checkbox
                if (selectors.ContainsKey("terms_checkbox") && success)
                {
                    var element = _browserManager.WaitForElement(selectors["terms_checkbox"]);
                    if (element != null && !element.Selected)
                    {
                        if (!await _browserManager.SafeClickAsync(element))
                        {
                            success = false;
                            errorMsg = "Lỗi khi click checkbox";
                        }
                    }
                }

                result.LastStep = RegistrationStep.SolvingCaptcha;

                // Xử lý captcha (nếu có)
                if (selectors.ContainsKey("captcha") && success)
                {
                    Logger.Warning("Phát hiện captcha - cần xử lý thủ công");
                    // TODO: Implement captcha solving
                }

                result.LastStep = RegistrationStep.SubmittingForm;

                // Submit form
                if (selectors.ContainsKey("submit_button") && success)
                {
                    var element = _browserManager.WaitForClickable(selectors["submit_button"]);
                    if (element != null)
                    {
                        // Chụp screenshot trước khi submit
                        var screenshotPath = Path.Combine(AppConfig.Instance.Output.ScreenshotFolder, 
                            $"before_submit_{result.Account.Username}.png");
                        _browserManager.TakeScreenshot(screenshotPath);

                        if (await _browserManager.SafeClickAsync(element))
                        {
                            result.LastStep = RegistrationStep.WaitingForResult;

                            // Chờ kết quả
                            await Task.Delay(5000);

                            // Chụp screenshot sau khi submit
                            screenshotPath = Path.Combine(AppConfig.Instance.Output.ScreenshotFolder, 
                                $"after_submit_{result.Account.Username}.png");
                            _browserManager.TakeScreenshot(screenshotPath);
                            result.ScreenshotPath = screenshotPath;

                            // Kiểm tra kết quả
                            var pageSource = _browserManager.GetPageSource();
                            if (!string.IsNullOrEmpty(pageSource))
                            {
                                result.Success = CheckRegistrationSuccess(pageSource);
                                if (!result.Success)
                                {
                                    errorMsg = ExtractErrorMessage(pageSource);
                                }
                            }
                        }
                        else
                        {
                            errorMsg = "Lỗi khi click submit";
                        }
                    }
                    else
                    {
                        errorMsg = "Không tìm thấy nút submit";
                    }
                }

                result.LastStep = RegistrationStep.Completed;
                result.Success = success && string.IsNullOrEmpty(errorMsg);
                result.ErrorMessage = errorMsg;

                if (_proxy != null)
                {
                    result.ProxyUsed = _proxy.FullAddress;
                }

                return result;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi đăng ký tài khoản: {Username}", result.Account.Username);
                result.ErrorMessage = ex.Message;
                return result;
            }
            finally
            {
                result.Duration = DateTime.Now - startTime;
                result.CompletedAt = DateTime.Now;
            }
        }

        /// <summary>
        /// Kiểm tra đăng ký thành công
        /// </summary>
        private bool CheckRegistrationSuccess(string pageSource)
        {
            var successIndicators = new[]
            {
                "注册成功", "registration successful", "welcome",
                "账号创建成功", "account created", "đăng ký thành công"
            };

            return successIndicators.Any(indicator => 
                pageSource.Contains(indicator, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Trích xuất thông báo lỗi
        /// </summary>
        private string ExtractErrorMessage(string pageSource)
        {
            var errorIndicators = new[]
            {
                "用户名已存在", "username exists", "email exists",
                "注册失败", "registration failed", "error",
                "đăng ký thất bại", "tài khoản đã tồn tại"
            };

            foreach (var indicator in errorIndicators)
            {
                if (pageSource.Contains(indicator, StringComparison.OrdinalIgnoreCase))
                {
                    return $"Đăng ký thất bại - {indicator}";
                }
            }

            return "Không xác định được kết quả đăng ký";
        }

        /// <summary>
        /// Lưu kết quả đăng ký
        /// </summary>
        public void SaveResult(RegistrationResult result)
        {
            try
            {
                if (result.Success)
                {
                    _accountGenerator.SaveAccount(result.Account, AppConfig.Instance.Output.SuccessFile);
                    Logger.LogRegistration(true, result.Account.Username, "Thành công");
                }
                else
                {
                    var failedLine = $"Failed: {result.Account.Username} - {result.ErrorMessage}{Environment.NewLine}";
                    File.AppendAllText(AppConfig.Instance.Output.FailedFile, failedLine);
                    Logger.LogRegistration(false, result.Account.Username, result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi lưu kết quả");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _browserManager?.Dispose();
                _disposed = true;
            }
        }
    }
}
