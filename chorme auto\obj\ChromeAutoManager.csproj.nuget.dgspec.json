{"format": 1, "restore": {"D:\\chorme auto c#\\chorme auto\\ChromeAutoManager.csproj": {}}, "projects": {"D:\\chorme auto c#\\chorme auto\\ChromeAutoManager.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\chorme auto c#\\chorme auto\\ChromeAutoManager.csproj", "projectName": "ChromeAutoManager", "projectPath": "D:\\chorme auto c#\\chorme auto\\ChromeAutoManager.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\chorme auto c#\\chorme auto\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Bogus": {"target": "Package", "version": "[34.0.2, )"}, "DotNetSeleniumExtras.WaitHelpers": {"target": "Package", "version": "[3.11.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Selenium.Support": {"target": "Package", "version": "[4.15.0, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.15.0, )"}, "Selenium.WebDriver.ChromeDriver": {"target": "Package", "version": "[119.0.6045.10500, )"}, "Serilog": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}