using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ChromeAutoManager.Core;
using ChromeAutoManager.Models;
using ChromeAutoManager.Services;

namespace ChromeAutoManager.Forms
{
    /// <summary>
    /// Form chính của ứng dụng
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly ProxyManager _proxyManager;
        private readonly AccountGenerator _accountGenerator;
        private int _successfulRegistrations = 0;
        private int _failedRegistrations = 0;
        private bool _isRunning = false;

        // Controls
        private GroupBox grpSettings;
        private GroupBox grpProgress;
        private GroupBox grpResults;
        private GroupBox grpAccountDetails;
        private NumericUpDown numAccountCount;
        private NumericUpDown numThreadCount;
        private CheckBox chkUseProxy;
        private CheckBox chkUseExistingBrowser;
        private RadioButton rbAutoGenerate;
        private RadioButton rbManualInput;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private TextBox txtFullName;
        private TextBox txtBankName;
        private Button btnStart;
        private Button btnStop;
        private Button btnAnalyzeForm;
        private Button btnSettings;
        private Button btnEditBank;
        private Button btnSaveBank;
        private ProgressBar progressBar;
        private Label lblProgress;
        private ListBox lstResults;
        private Label lblStats;
        private RichTextBox rtbLog;
        private DataGridView dgvAccounts;

        public MainForm()
        {
            try
            {
                // Show startup message
                MessageBox.Show("Đang khởi tạo Chrome Auto Manager...", "Khởi động", MessageBoxButtons.OK, MessageBoxIcon.Information);

                _proxyManager = new ProxyManager();
                _accountGenerator = new AccountGenerator();

                MessageBox.Show("Đang tạo giao diện...", "Khởi động", MessageBoxButtons.OK, MessageBoxIcon.Information);

                InitializeComponent();
                InitializeUI();

                // Force show window
                this.WindowState = FormWindowState.Normal;
                this.BringToFront();
                this.Activate();

                MessageBox.Show("Khởi tạo hoàn tất!", "Thành công", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khởi tạo ứng dụng: {ex.Message}\n\nStack trace:\n{ex.StackTrace}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Logger.Error(ex, "Lỗi khởi tạo MainForm");
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "Chrome Auto Manager - Tool Đăng Ký Tài Khoản Tự Động";
            this.Size = new Size(1400, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1200, 700);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.BackColor = Color.FromArgb(240, 240, 240);

            // Initialize controls
            InitializeControls();
            LayoutControls();

            this.ResumeLayout(false);
        }

        private void InitializeControls()
        {
            // Create a main panel for better layout control
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 2,
                Padding = new Padding(10),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            // Set column and row styles - 3 columns layout
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 350F)); // Settings
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));   // Results
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));   // Account Details
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 250F));       // Top row
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));        // Bottom row

            // Settings GroupBox
            grpSettings = new GroupBox
            {
                Text = "⚙️ Cài Đặt",
                Dock = DockStyle.Fill,
                Margin = new Padding(5),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 33, 33),
                BackColor = Color.White
            };

            // Create settings panel with flow layout
            var settingsPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.TopDown,
                Padding = new Padding(10),
                AutoScroll = true
            };

            // Row 1: Account and Thread count
            var row1Panel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = false,
                Margin = new Padding(0, 5, 0, 5)
            };

            var lblAccountCount = new Label
            {
                Text = "Số tài khoản:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 3, 5, 0)
            };
            numAccountCount = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 100,
                Value = 1,
                Size = new Size(60, 23),
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 0, 15, 0)
            };

            var lblThreadCount = new Label
            {
                Text = "Số luồng:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 3, 5, 0)
            };
            numThreadCount = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 10,
                Value = 1,
                Size = new Size(60, 23),
                Font = new Font("Segoe UI", 9)
            };

            row1Panel.Controls.AddRange(new Control[] { lblAccountCount, numAccountCount, lblThreadCount, numThreadCount });

            // Row 2: Checkboxes
            var row2Panel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = true,
                Margin = new Padding(0, 5, 0, 5)
            };

            chkUseProxy = new CheckBox
            {
                Text = "✓ Sử dụng proxy",
                AutoSize = true,
                Checked = true,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 0, 15, 0)
            };

            chkUseExistingBrowser = new CheckBox
            {
                Text = "✓ Sử dụng trình duyệt hiện có",
                AutoSize = true,
                Checked = true,
                Font = new Font("Segoe UI", 9)
            };

            row2Panel.Controls.AddRange(new Control[] { chkUseProxy, chkUseExistingBrowser });

            // Row 3: Radio buttons
            var row3Panel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = true,
                Margin = new Padding(0, 5, 0, 5)
            };

            rbAutoGenerate = new RadioButton
            {
                Text = "🔄 Tự động tạo thông tin",
                AutoSize = true,
                Checked = true,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 0, 15, 0)
            };

            rbManualInput = new RadioButton
            {
                Text = "✏️ Nhập thông tin thủ công",
                AutoSize = true,
                Font = new Font("Segoe UI", 9)
            };

            row3Panel.Controls.AddRange(new Control[] { rbAutoGenerate, rbManualInput });

            // Manual input fields (initially hidden)
            var manualInputPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Visible = false,
                Margin = new Padding(0, 10, 0, 5)
            };
            manualInputPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            manualInputPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            var lblUsername = new Label
            {
                Text = "Tên đăng nhập:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 3, 10, 3)
            };
            txtUsername = new TextBox
            {
                Dock = DockStyle.Fill,
                Enabled = false,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 0, 0, 5)
            };

            var lblPassword = new Label
            {
                Text = "Mật khẩu:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 3, 10, 3)
            };
            txtPassword = new TextBox
            {
                Dock = DockStyle.Fill,
                Enabled = false,
                UseSystemPasswordChar = true,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 0, 0, 5)
            };

            var lblFullName = new Label
            {
                Text = "Họ tên:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 3, 10, 3)
            };
            txtFullName = new TextBox
            {
                Dock = DockStyle.Fill,
                Enabled = false,
                Font = new Font("Segoe UI", 9)
            };

            manualInputPanel.Controls.Add(lblUsername, 0, 0);
            manualInputPanel.Controls.Add(txtUsername, 1, 0);
            manualInputPanel.Controls.Add(lblPassword, 0, 1);
            manualInputPanel.Controls.Add(txtPassword, 1, 1);
            manualInputPanel.Controls.Add(lblFullName, 0, 2);
            manualInputPanel.Controls.Add(txtFullName, 1, 2);

            // Buttons panel
            var buttonsPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = true,
                Margin = new Padding(0, 10, 0, 0)
            };

            btnStart = new Button
            {
                Text = "🚀 Bắt Đầu",
                Size = new Size(90, 35),
                BackColor = Color.FromArgb(46, 125, 50),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                Margin = new Padding(0, 0, 5, 0)
            };

            btnStop = new Button
            {
                Text = "⏹️ Dừng",
                Size = new Size(90, 35),
                BackColor = Color.FromArgb(211, 47, 47),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                Enabled = false,
                Margin = new Padding(0, 0, 5, 0)
            };

            btnAnalyzeForm = new Button
            {
                Text = "🔍 Phân Tích",
                Size = new Size(90, 35),
                BackColor = Color.FromArgb(25, 118, 210),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                Margin = new Padding(0, 0, 5, 0)
            };

            btnSettings = new Button
            {
                Text = "⚙️ Cài Đặt",
                Size = new Size(90, 35),
                BackColor = Color.FromArgb(117, 117, 117),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9)
            };

            buttonsPanel.Controls.AddRange(new Control[] { btnStart, btnStop, btnAnalyzeForm, btnSettings });

            // Add all rows to settings panel
            settingsPanel.Controls.AddRange(new Control[] { row1Panel, row2Panel, row3Panel, manualInputPanel, buttonsPanel });
            grpSettings.Controls.Add(settingsPanel);

            // Progress GroupBox
            grpProgress = new GroupBox
            {
                Text = "📊 Tiến Trình",
                Dock = DockStyle.Fill,
                Margin = new Padding(5),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 33, 33),
                BackColor = Color.White
            };

            var progressPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1,
                Padding = new Padding(10)
            };
            progressPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            progressPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            progressPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            progressBar = new ProgressBar
            {
                Dock = DockStyle.Fill,
                Height = 25,
                Style = ProgressBarStyle.Continuous,
                Margin = new Padding(0, 0, 0, 10)
            };

            lblProgress = new Label
            {
                Text = "🔄 Sẵn sàng...",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(33, 33, 33),
                Margin = new Padding(0, 0, 0, 5)
            };

            lblStats = new Label
            {
                Text = "📈 Thành công: 0 | Thất bại: 0 | Tỷ lệ: 0%",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210)
            };

            progressPanel.Controls.Add(progressBar, 0, 0);
            progressPanel.Controls.Add(lblProgress, 0, 1);
            progressPanel.Controls.Add(lblStats, 0, 2);
            grpProgress.Controls.Add(progressPanel);

            // Results GroupBox
            grpResults = new GroupBox
            {
                Text = "📋 Kết Quả",
                Dock = DockStyle.Fill,
                Margin = new Padding(5),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 33, 33),
                BackColor = Color.White
            };

            var resultsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1,
                Padding = new Padding(10)
            };
            resultsPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 40F));
            resultsPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            resultsPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 60F));

            lstResults = new ListBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(0, 0, 0, 10)
            };

            var lblLog = new Label
            {
                Text = "📝 Log Chi Tiết:",
                AutoSize = true,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                Margin = new Padding(0, 0, 0, 5)
            };

            rtbLog = new RichTextBox
            {
                Dock = DockStyle.Fill,
                ReadOnly = true,
                BackColor = Color.FromArgb(30, 30, 30),
                ForeColor = Color.FromArgb(220, 220, 220),
                Font = new Font("Consolas", 9),
                BorderStyle = BorderStyle.FixedSingle
            };

            resultsPanel.Controls.Add(lstResults, 0, 0);
            resultsPanel.Controls.Add(lblLog, 0, 1);
            resultsPanel.Controls.Add(rtbLog, 0, 2);
            grpResults.Controls.Add(resultsPanel);

            // Account Details GroupBox
            grpAccountDetails = new GroupBox
            {
                Text = "💳 Chi Tiết Tài Khoản",
                Dock = DockStyle.Fill,
                Margin = new Padding(5),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 33, 33),
                BackColor = Color.White
            };

            var accountDetailsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1,
                Padding = new Padding(10)
            };
            accountDetailsPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // Bank name section
            accountDetailsPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F)); // DataGridView
            accountDetailsPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // Summary

            // Bank name section
            var bankPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = false,
                Margin = new Padding(0, 0, 0, 10)
            };

            var lblBankName = new Label
            {
                Text = "🏦 Tên ngân hàng:",
                AutoSize = true,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                Margin = new Padding(0, 3, 10, 0)
            };

            txtBankName = new TextBox
            {
                Text = "Vietcombank",
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 9),
                ReadOnly = true,
                Margin = new Padding(0, 0, 5, 0)
            };

            btnEditBank = new Button
            {
                Text = "✏️ Sửa",
                Size = new Size(60, 25),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 8),
                Margin = new Padding(0, 0, 5, 0)
            };

            btnSaveBank = new Button
            {
                Text = "💾 Lưu",
                Size = new Size(60, 25),
                BackColor = Color.FromArgb(46, 125, 50),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 8),
                Visible = false
            };

            bankPanel.Controls.AddRange(new Control[] { lblBankName, txtBankName, btnEditBank, btnSaveBank });

            // DataGridView for accounts
            dgvAccounts = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle,
                Font = new Font("Segoe UI", 9),
                RowHeadersVisible = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Add columns
            dgvAccounts.Columns.Add("STT", "STT");
            dgvAccounts.Columns.Add("Username", "Tài khoản");
            dgvAccounts.Columns.Add("Password", "Mật khẩu");
            dgvAccounts.Columns.Add("FullName", "Họ tên");
            dgvAccounts.Columns.Add("BankName", "Ngân hàng");
            dgvAccounts.Columns.Add("Status", "Trạng thái");

            // Set column widths
            dgvAccounts.Columns["STT"].FillWeight = 10;
            dgvAccounts.Columns["Username"].FillWeight = 20;
            dgvAccounts.Columns["Password"].FillWeight = 20;
            dgvAccounts.Columns["FullName"].FillWeight = 25;
            dgvAccounts.Columns["BankName"].FillWeight = 15;
            dgvAccounts.Columns["Status"].FillWeight = 10;

            // Summary label
            var lblAccountSummary = new Label
            {
                Text = "📊 Tổng: 0 tài khoản | Thành công: 0 | Thất bại: 0",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 10, 0, 0)
            };

            accountDetailsPanel.Controls.Add(bankPanel, 0, 0);
            accountDetailsPanel.Controls.Add(dgvAccounts, 0, 1);
            accountDetailsPanel.Controls.Add(lblAccountSummary, 0, 2);
            grpAccountDetails.Controls.Add(accountDetailsPanel);

            // Add groups to main panel
            mainPanel.Controls.Add(grpSettings, 0, 0);
            mainPanel.Controls.Add(grpProgress, 0, 1);
            mainPanel.Controls.Add(grpResults, 1, 0);
            mainPanel.SetRowSpan(grpResults, 2); // Results spans both rows
            mainPanel.Controls.Add(grpAccountDetails, 2, 0);
            mainPanel.SetRowSpan(grpAccountDetails, 2); // Account Details spans both rows

            // Add main panel to form
            this.Controls.Add(mainPanel);

            // Store reference to manual input panel for show/hide
            this.manualInputPanel = manualInputPanel;
        }

        private TableLayoutPanel? manualInputPanel;

        private void LayoutControls()
        {
            // Layout is handled by TableLayoutPanel and FlowLayoutPanel
            // No manual resize handling needed
        }

        private void InitializeUI()
        {
            try
            {
                // Set form font
                this.Font = new Font("Segoe UI", 9);

                // Event handlers
                rbAutoGenerate.CheckedChanged += RbMode_CheckedChanged;
                rbManualInput.CheckedChanged += RbMode_CheckedChanged;
                btnStart.Click += BtnStart_Click;
                btnStop.Click += BtnStop_Click;
                btnAnalyzeForm.Click += BtnAnalyzeForm_Click;
                btnSettings.Click += BtnSettings_Click;
                btnEditBank.Click += BtnEditBank_Click;
                btnSaveBank.Click += BtnSaveBank_Click;

                // Initialize log
                LogMessage("🚀 Ứng dụng Chrome Auto Manager đã khởi động", Color.FromArgb(46, 125, 50));
                LogMessage($"🌐 Đường dẫn đăng ký: {AppConfig.Instance.RegisterUrl}", Color.FromArgb(25, 118, 210));
                LogMessage("💡 Sẵn sàng để bắt đầu quá trình đăng ký tài khoản", Color.FromArgb(117, 117, 117));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khởi tạo UI: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Logger.Error(ex, "Lỗi khởi tạo UI");
            }
        }

        private void RbMode_CheckedChanged(object? sender, EventArgs e)
        {
            var isManual = rbManualInput.Checked;
            txtUsername.Enabled = isManual;
            txtPassword.Enabled = isManual;
            txtFullName.Enabled = isManual;

            // Show/hide manual input panel
            if (manualInputPanel != null)
            {
                manualInputPanel.Visible = isManual;
            }

            if (isManual)
            {
                numAccountCount.Value = 1;
                numAccountCount.Enabled = false;
                numThreadCount.Value = 1;
                numThreadCount.Enabled = false;
            }
            else
            {
                numAccountCount.Enabled = true;
                numThreadCount.Enabled = true;
            }
        }

        private async void BtnStart_Click(object? sender, EventArgs e)
        {
            if (_isRunning) return;

            try
            {
                _isRunning = true;
                btnStart.Enabled = false;
                btnStop.Enabled = true;

                // Reset counters
                _successfulRegistrations = 0;
                _failedRegistrations = 0;
                lstResults.Items.Clear();
                dgvAccounts.Rows.Clear();
                UpdateStats();
                UpdateAccountSummary();

                LogMessage("Bắt đầu quá trình đăng ký...", Color.Yellow);

                // Prepare proxies if needed
                if (chkUseProxy.Checked)
                {
                    LogMessage("Đang chuẩn bị proxy...", Color.Cyan);
                    var loadedProxies = _proxyManager.LoadWorkingProxies();

                    if (loadedProxies < 5)
                    {
                        LogMessage("Đang tìm kiếm proxy mới...", Color.Cyan);
                        var foundProxies = await _proxyManager.FetchAndTestProxiesAsync();
                        LogMessage($"Tìm thấy {foundProxies} proxy hoạt động", Color.Green);
                    }
                    else
                    {
                        LogMessage($"Đã load {loadedProxies} proxy từ file", Color.Green);
                    }
                }

                // Start registration process
                await StartRegistrationProcess();
            }
            catch (Exception ex)
            {
                LogMessage($"Lỗi: {ex.Message}", Color.Red);
                Logger.Error(ex, "Lỗi trong quá trình đăng ký");
            }
            finally
            {
                _isRunning = false;
                btnStart.Enabled = true;
                btnStop.Enabled = false;
                LogMessage("Quá trình đăng ký đã hoàn thành", Color.Green);
            }
        }

        private void BtnStop_Click(object? sender, EventArgs e)
        {
            _isRunning = false;
            LogMessage("Đang dừng quá trình...", Color.Yellow);
        }

        private async void BtnAnalyzeForm_Click(object? sender, EventArgs e)
        {
            try
            {
                LogMessage("Đang phân tích form đăng ký...", Color.Cyan);

                using var bot = new RegistrationBot();
                var selectors = await bot.AnalyzeFormAsync();

                if (selectors != null && selectors.Any())
                {
                    LogMessage($"Phân tích thành công! Tìm thấy {selectors.Count} trường:", Color.Green);
                    foreach (var selector in selectors)
                    {
                        LogMessage($"  - {selector.Key}: {selector.Value}", Color.White);
                    }
                }
                else
                {
                    LogMessage("Không thể phân tích form đăng ký!", Color.Red);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Lỗi khi phân tích form: {ex.Message}", Color.Red);
                Logger.Error(ex, "Lỗi khi phân tích form");
            }
        }

        private void BtnSettings_Click(object? sender, EventArgs e)
        {
            try
            {
                using var settingsForm = new SettingsForm();
                if (settingsForm.ShowDialog(this) == DialogResult.OK)
                {
                    LogMessage("Cài đặt đã được cập nhật", Color.Green);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Lỗi khi mở cài đặt: {ex.Message}", Color.Red);
                Logger.Error(ex, "Lỗi khi mở form cài đặt");
            }
        }

        private void BtnEditBank_Click(object? sender, EventArgs e)
        {
            txtBankName.ReadOnly = false;
            txtBankName.Focus();
            txtBankName.SelectAll();
            btnEditBank.Visible = false;
            btnSaveBank.Visible = true;
        }

        private void BtnSaveBank_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtBankName.Text))
            {
                MessageBox.Show("Tên ngân hàng không được để trống!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            txtBankName.ReadOnly = true;
            btnEditBank.Visible = true;
            btnSaveBank.Visible = false;

            // Update all existing accounts with new bank name
            UpdateBankNameInAccounts(txtBankName.Text.Trim());

            LogMessage($"🏦 Đã cập nhật tên ngân hàng: {txtBankName.Text.Trim()}", Color.FromArgb(25, 118, 210));
        }

        private void UpdateBankNameInAccounts(string newBankName)
        {
            foreach (DataGridViewRow row in dgvAccounts.Rows)
            {
                if (row.Cells["BankName"].Value != null)
                {
                    row.Cells["BankName"].Value = newBankName;
                }
            }
        }

        private void AddAccountToGrid(int index, Account account, string status)
        {
            var row = new DataGridViewRow();
            row.CreateCells(dgvAccounts);

            row.Cells[0].Value = index; // STT
            row.Cells[1].Value = account.Username; // Username
            row.Cells[2].Value = account.Password; // Password
            row.Cells[3].Value = account.FullName; // FullName
            row.Cells[4].Value = txtBankName.Text.Trim(); // BankName
            row.Cells[5].Value = status; // Status

            // Set row color based on status
            if (status.Contains("Thành công"))
            {
                row.DefaultCellStyle.BackColor = Color.FromArgb(232, 245, 233);
                row.DefaultCellStyle.ForeColor = Color.FromArgb(27, 94, 32);
            }
            else
            {
                row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 238);
                row.DefaultCellStyle.ForeColor = Color.FromArgb(183, 28, 28);
            }

            dgvAccounts.Rows.Add(row);
        }

        private void UpdateAccountSummary()
        {
            var total = dgvAccounts.Rows.Count;
            var successful = dgvAccounts.Rows.Cast<DataGridViewRow>()
                .Count(r => r.Cells["Status"].Value?.ToString()?.Contains("Thành công") == true);
            var failed = total - successful;

            // Find and update the summary label
            var summaryLabel = grpAccountDetails.Controls
                .OfType<TableLayoutPanel>().FirstOrDefault()?
                .Controls.OfType<Label>().FirstOrDefault(l => l.Text.StartsWith("📊"));

            if (summaryLabel != null)
            {
                summaryLabel.Text = $"📊 Tổng: {total} tài khoản | Thành công: {successful} | Thất bại: {failed}";
            }
        }

        private async Task StartRegistrationProcess()
        {
            var accountCount = (int)numAccountCount.Value;
            var threadCount = (int)numThreadCount.Value;
            var useProxy = chkUseProxy.Checked;
            var isManual = rbManualInput.Checked;

            progressBar.Maximum = accountCount;
            progressBar.Value = 0;

            // Create manual account if needed
            Account? manualAccount = null;
            if (isManual)
            {
                manualAccount = new Account
                {
                    Username = txtUsername.Text,
                    Password = txtPassword.Text,
                    FullName = txtFullName.Text
                };

                if (!manualAccount.IsValid())
                {
                    LogMessage("Thông tin tài khoản thủ công không hợp lệ!", Color.Red);
                    return;
                }
            }

            // Analyze form first
            Dictionary<string, string>? selectors;
            using (var bot = new RegistrationBot())
            {
                selectors = await bot.AnalyzeFormAsync();
            }

            if (selectors == null || !selectors.Any())
            {
                LogMessage("Không thể phân tích form đăng ký!", Color.Red);
                return;
            }

            LogMessage($"Bắt đầu đăng ký {accountCount} tài khoản với {threadCount} luồng", Color.Yellow);

            // Process registrations
            var tasks = new List<Task>();
            var semaphore = new SemaphoreSlim(threadCount);

            for (int i = 0; i < accountCount && _isRunning; i++)
            {
                var accountIndex = i + 1;
                var task = ProcessSingleRegistration(semaphore, selectors, accountIndex, useProxy, manualAccount);
                tasks.Add(task);
            }

            await Task.WhenAll(tasks);
        }

        private async Task ProcessSingleRegistration(SemaphoreSlim semaphore, Dictionary<string, string> selectors,
            int accountIndex, bool useProxy, Account? manualAccount)
        {
            await semaphore.WaitAsync();
            try
            {
                if (!_isRunning) return;

                LogMessage($"Bắt đầu đăng ký tài khoản #{accountIndex}", Color.Cyan);

                // Get proxy if needed
                ProxyInfo? proxy = null;
                if (useProxy)
                {
                    proxy = _proxyManager.GetProxy();
                    if (proxy == null)
                    {
                        LogMessage($"Không có proxy cho tài khoản #{accountIndex}", Color.Yellow);
                    }
                }

                // Register account
                using var bot = new RegistrationBot(proxy);
                var result = await bot.RegisterAccountAsync(selectors, manualAccount);

                // Save result
                bot.SaveResult(result);

                // Update UI
                this.Invoke(() =>
                {
                    if (result.Success)
                    {
                        _successfulRegistrations++;
                        LogMessage($"✓ Tài khoản #{accountIndex}: {result.Account.Username} - THÀNH CÔNG", Color.Green);
                        lstResults.Items.Add($"✓ {result.Account.Username} - {result.Account.Password}");

                        // Add to DataGridView
                        AddAccountToGrid(accountIndex, result.Account, "✅ Thành công");
                    }
                    else
                    {
                        _failedRegistrations++;
                        LogMessage($"✗ Tài khoản #{accountIndex}: THẤT BẠI - {result.ErrorMessage}", Color.Red);
                        lstResults.Items.Add($"✗ {result.Account.Username} - {result.ErrorMessage}");

                        // Add to DataGridView
                        AddAccountToGrid(accountIndex, result.Account, "❌ Thất bại");
                    }

                    progressBar.Value++;
                    UpdateStats();
                    UpdateProgress();
                    UpdateAccountSummary();
                });

                // Release proxy
                if (proxy != null)
                {
                    _proxyManager.ReleaseProxy(proxy);
                }
            }
            finally
            {
                semaphore.Release();
            }
        }

        private void UpdateStats()
        {
            var total = _successfulRegistrations + _failedRegistrations;
            var successRate = total > 0 ? (_successfulRegistrations * 100.0 / total) : 0;
            lblStats.Text = $"Thành công: {_successfulRegistrations} | Thất bại: {_failedRegistrations} | Tỷ lệ: {successRate:F1}%";
        }

        private void UpdateProgress()
        {
            var completed = progressBar.Value;
            var total = progressBar.Maximum;
            lblProgress.Text = $"Đã hoàn thành: {completed}/{total} tài khoản";
        }

        private void LogMessage(string message, Color color)
        {
            if (rtbLog.InvokeRequired)
            {
                rtbLog.Invoke(() => LogMessage(message, color));
                return;
            }

            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            rtbLog.SelectionStart = rtbLog.TextLength;
            rtbLog.SelectionLength = 0;
            rtbLog.SelectionColor = Color.Gray;
            rtbLog.AppendText($"[{timestamp}] ");
            rtbLog.SelectionColor = color;
            rtbLog.AppendText($"{message}\n");
            rtbLog.ScrollToCaret();

            // Keep only last 1000 lines
            if (rtbLog.Lines.Length > 1000)
            {
                var lines = rtbLog.Lines.Skip(100).ToArray();
                rtbLog.Lines = lines;
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_isRunning)
            {
                var result = MessageBox.Show("Quá trình đăng ký đang chạy. Bạn có muốn dừng và thoát?",
                    "Xác nhận", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }

                _isRunning = false;
            }

            _proxyManager?.Dispose();
            Logger.CloseAndFlush();
            base.OnFormClosing(e);
        }
    }
}
