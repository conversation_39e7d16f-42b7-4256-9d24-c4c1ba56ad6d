using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ChromeAutoManager.Core;
using ChromeAutoManager.Models;
using ChromeAutoManager.Services;

namespace ChromeAutoManager.Forms
{
    /// <summary>
    /// Form chính của ứng dụng
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly ProxyManager _proxyManager;
        private readonly AccountGenerator _accountGenerator;
        private int _successfulRegistrations = 0;
        private int _failedRegistrations = 0;
        private bool _isRunning = false;

        // Controls
        private GroupBox grpSettings;
        private GroupBox grpProgress;
        private GroupBox grpResults;
        private NumericUpDown numAccountCount;
        private NumericUpDown numThreadCount;
        private CheckBox chkUseProxy;
        private CheckBox chkUseExistingBrowser;
        private RadioButton rbAutoGenerate;
        private RadioButton rbManualInput;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private TextBox txtFullName;
        private Button btnStart;
        private Button btnStop;
        private Button btnAnalyzeForm;
        private Button btnSettings;
        private ProgressBar progressBar;
        private Label lblProgress;
        private ListBox lstResults;
        private Label lblStats;
        private RichTextBox rtbLog;

        public MainForm()
        {
            _proxyManager = new ProxyManager();
            _accountGenerator = new AccountGenerator();
            InitializeComponent();
            InitializeUI();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "Chrome Auto Manager - Tool Đăng Ký Tài Khoản Tự Động";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(800, 600);

            // Initialize controls
            InitializeControls();
            LayoutControls();

            this.ResumeLayout(false);
        }

        private void InitializeControls()
        {
            // Settings GroupBox
            grpSettings = new GroupBox
            {
                Text = "Cài Đặt",
                Size = new Size(480, 200),
                Location = new Point(10, 10)
            };

            // Account count
            var lblAccountCount = new Label
            {
                Text = "Số lượng tài khoản:",
                Location = new Point(10, 25),
                Size = new Size(120, 20)
            };
            numAccountCount = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 100,
                Value = 1,
                Location = new Point(140, 23),
                Size = new Size(60, 20)
            };

            // Thread count
            var lblThreadCount = new Label
            {
                Text = "Số luồng:",
                Location = new Point(220, 25),
                Size = new Size(60, 20)
            };
            numThreadCount = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 10,
                Value = 1,
                Location = new Point(290, 23),
                Size = new Size(60, 20)
            };

            // Checkboxes
            chkUseProxy = new CheckBox
            {
                Text = "Sử dụng proxy",
                Location = new Point(10, 55),
                Size = new Size(120, 20),
                Checked = true
            };

            chkUseExistingBrowser = new CheckBox
            {
                Text = "Sử dụng trình duyệt hiện có",
                Location = new Point(140, 55),
                Size = new Size(180, 20),
                Checked = true
            };

            // Radio buttons for account mode
            rbAutoGenerate = new RadioButton
            {
                Text = "Tự động tạo thông tin",
                Location = new Point(10, 85),
                Size = new Size(150, 20),
                Checked = true
            };

            rbManualInput = new RadioButton
            {
                Text = "Nhập thông tin thủ công",
                Location = new Point(170, 85),
                Size = new Size(150, 20)
            };

            // Manual input fields
            var lblUsername = new Label
            {
                Text = "Tên đăng nhập:",
                Location = new Point(10, 115),
                Size = new Size(100, 20)
            };
            txtUsername = new TextBox
            {
                Location = new Point(120, 113),
                Size = new Size(100, 20),
                Enabled = false
            };

            var lblPassword = new Label
            {
                Text = "Mật khẩu:",
                Location = new Point(230, 115),
                Size = new Size(60, 20)
            };
            txtPassword = new TextBox
            {
                Location = new Point(300, 113),
                Size = new Size(100, 20),
                Enabled = false
            };

            var lblFullName = new Label
            {
                Text = "Họ tên:",
                Location = new Point(10, 145),
                Size = new Size(50, 20)
            };
            txtFullName = new TextBox
            {
                Location = new Point(70, 143),
                Size = new Size(150, 20),
                Enabled = false
            };

            // Buttons
            btnStart = new Button
            {
                Text = "Bắt Đầu",
                Location = new Point(10, 170),
                Size = new Size(80, 25),
                BackColor = Color.Green,
                ForeColor = Color.White
            };

            btnStop = new Button
            {
                Text = "Dừng",
                Location = new Point(100, 170),
                Size = new Size(80, 25),
                BackColor = Color.Red,
                ForeColor = Color.White,
                Enabled = false
            };

            btnAnalyzeForm = new Button
            {
                Text = "Phân Tích Form",
                Location = new Point(190, 170),
                Size = new Size(100, 25)
            };

            btnSettings = new Button
            {
                Text = "Cài Đặt",
                Location = new Point(300, 170),
                Size = new Size(80, 25)
            };

            // Add controls to settings group
            grpSettings.Controls.AddRange(new Control[]
            {
                lblAccountCount, numAccountCount, lblThreadCount, numThreadCount,
                chkUseProxy, chkUseExistingBrowser, rbAutoGenerate, rbManualInput,
                lblUsername, txtUsername, lblPassword, txtPassword, lblFullName, txtFullName,
                btnStart, btnStop, btnAnalyzeForm, btnSettings
            });

            // Progress GroupBox
            grpProgress = new GroupBox
            {
                Text = "Tiến Trình",
                Size = new Size(480, 100),
                Location = new Point(10, 220)
            };

            progressBar = new ProgressBar
            {
                Location = new Point(10, 25),
                Size = new Size(460, 20)
            };

            lblProgress = new Label
            {
                Text = "Sẵn sàng...",
                Location = new Point(10, 55),
                Size = new Size(460, 20)
            };

            lblStats = new Label
            {
                Text = "Thành công: 0 | Thất bại: 0",
                Location = new Point(10, 75),
                Size = new Size(460, 20)
            };

            grpProgress.Controls.AddRange(new Control[] { progressBar, lblProgress, lblStats });

            // Results GroupBox
            grpResults = new GroupBox
            {
                Text = "Kết Quả",
                Size = new Size(480, 320),
                Location = new Point(500, 10)
            };

            lstResults = new ListBox
            {
                Location = new Point(10, 25),
                Size = new Size(460, 150)
            };

            rtbLog = new RichTextBox
            {
                Location = new Point(10, 185),
                Size = new Size(460, 125),
                ReadOnly = true,
                BackColor = Color.Black,
                ForeColor = Color.White,
                Font = new Font("Consolas", 8)
            };

            grpResults.Controls.AddRange(new Control[] { lstResults, rtbLog });

            // Add main groups to form
            this.Controls.AddRange(new Control[] { grpSettings, grpProgress, grpResults });
        }

        private void LayoutControls()
        {
            // Auto-resize results group when form resizes
            this.Resize += (s, e) =>
            {
                grpResults.Size = new Size(this.Width - grpResults.Left - 20, this.Height - grpResults.Top - 50);
                lstResults.Size = new Size(grpResults.Width - 20, 150);
                rtbLog.Size = new Size(grpResults.Width - 20, grpResults.Height - rtbLog.Top - 20);
            };
        }

        private void InitializeUI()
        {
            // Event handlers
            rbAutoGenerate.CheckedChanged += RbMode_CheckedChanged;
            rbManualInput.CheckedChanged += RbMode_CheckedChanged;
            btnStart.Click += BtnStart_Click;
            btnStop.Click += BtnStop_Click;
            btnAnalyzeForm.Click += BtnAnalyzeForm_Click;
            btnSettings.Click += BtnSettings_Click;

            // Initialize log
            LogMessage("Ứng dụng đã khởi động", Color.Green);
            LogMessage($"Đường dẫn đăng ký: {AppConfig.Instance.RegisterUrl}", Color.Cyan);
        }

        private void RbMode_CheckedChanged(object? sender, EventArgs e)
        {
            var isManual = rbManualInput.Checked;
            txtUsername.Enabled = isManual;
            txtPassword.Enabled = isManual;
            txtFullName.Enabled = isManual;

            if (isManual)
            {
                numAccountCount.Value = 1;
                numAccountCount.Enabled = false;
                numThreadCount.Value = 1;
                numThreadCount.Enabled = false;
            }
            else
            {
                numAccountCount.Enabled = true;
                numThreadCount.Enabled = true;
            }
        }

        private async void BtnStart_Click(object? sender, EventArgs e)
        {
            if (_isRunning) return;

            try
            {
                _isRunning = true;
                btnStart.Enabled = false;
                btnStop.Enabled = true;

                // Reset counters
                _successfulRegistrations = 0;
                _failedRegistrations = 0;
                UpdateStats();

                LogMessage("Bắt đầu quá trình đăng ký...", Color.Yellow);

                // Prepare proxies if needed
                if (chkUseProxy.Checked)
                {
                    LogMessage("Đang chuẩn bị proxy...", Color.Cyan);
                    var loadedProxies = _proxyManager.LoadWorkingProxies();

                    if (loadedProxies < 5)
                    {
                        LogMessage("Đang tìm kiếm proxy mới...", Color.Cyan);
                        var foundProxies = await _proxyManager.FetchAndTestProxiesAsync();
                        LogMessage($"Tìm thấy {foundProxies} proxy hoạt động", Color.Green);
                    }
                    else
                    {
                        LogMessage($"Đã load {loadedProxies} proxy từ file", Color.Green);
                    }
                }

                // Start registration process
                await StartRegistrationProcess();
            }
            catch (Exception ex)
            {
                LogMessage($"Lỗi: {ex.Message}", Color.Red);
                Logger.Error(ex, "Lỗi trong quá trình đăng ký");
            }
            finally
            {
                _isRunning = false;
                btnStart.Enabled = true;
                btnStop.Enabled = false;
                LogMessage("Quá trình đăng ký đã hoàn thành", Color.Green);
            }
        }

        private void BtnStop_Click(object? sender, EventArgs e)
        {
            _isRunning = false;
            LogMessage("Đang dừng quá trình...", Color.Yellow);
        }

        private async void BtnAnalyzeForm_Click(object? sender, EventArgs e)
        {
            try
            {
                LogMessage("Đang phân tích form đăng ký...", Color.Cyan);

                using var bot = new RegistrationBot();
                var selectors = await bot.AnalyzeFormAsync();

                if (selectors != null && selectors.Any())
                {
                    LogMessage($"Phân tích thành công! Tìm thấy {selectors.Count} trường:", Color.Green);
                    foreach (var selector in selectors)
                    {
                        LogMessage($"  - {selector.Key}: {selector.Value}", Color.White);
                    }
                }
                else
                {
                    LogMessage("Không thể phân tích form đăng ký!", Color.Red);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Lỗi khi phân tích form: {ex.Message}", Color.Red);
                Logger.Error(ex, "Lỗi khi phân tích form");
            }
        }

        private void BtnSettings_Click(object? sender, EventArgs e)
        {
            try
            {
                using var settingsForm = new SettingsForm();
                if (settingsForm.ShowDialog(this) == DialogResult.OK)
                {
                    LogMessage("Cài đặt đã được cập nhật", Color.Green);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Lỗi khi mở cài đặt: {ex.Message}", Color.Red);
                Logger.Error(ex, "Lỗi khi mở form cài đặt");
            }
        }

        private async Task StartRegistrationProcess()
        {
            var accountCount = (int)numAccountCount.Value;
            var threadCount = (int)numThreadCount.Value;
            var useProxy = chkUseProxy.Checked;
            var isManual = rbManualInput.Checked;

            progressBar.Maximum = accountCount;
            progressBar.Value = 0;

            // Create manual account if needed
            Account? manualAccount = null;
            if (isManual)
            {
                manualAccount = new Account
                {
                    Username = txtUsername.Text,
                    Password = txtPassword.Text,
                    FullName = txtFullName.Text
                };

                if (!manualAccount.IsValid())
                {
                    LogMessage("Thông tin tài khoản thủ công không hợp lệ!", Color.Red);
                    return;
                }
            }

            // Analyze form first
            Dictionary<string, string>? selectors;
            using (var bot = new RegistrationBot())
            {
                selectors = await bot.AnalyzeFormAsync();
            }

            if (selectors == null || !selectors.Any())
            {
                LogMessage("Không thể phân tích form đăng ký!", Color.Red);
                return;
            }

            LogMessage($"Bắt đầu đăng ký {accountCount} tài khoản với {threadCount} luồng", Color.Yellow);

            // Process registrations
            var tasks = new List<Task>();
            var semaphore = new SemaphoreSlim(threadCount);

            for (int i = 0; i < accountCount && _isRunning; i++)
            {
                var accountIndex = i + 1;
                var task = ProcessSingleRegistration(semaphore, selectors, accountIndex, useProxy, manualAccount);
                tasks.Add(task);
            }

            await Task.WhenAll(tasks);
        }

        private async Task ProcessSingleRegistration(SemaphoreSlim semaphore, Dictionary<string, string> selectors,
            int accountIndex, bool useProxy, Account? manualAccount)
        {
            await semaphore.WaitAsync();
            try
            {
                if (!_isRunning) return;

                LogMessage($"Bắt đầu đăng ký tài khoản #{accountIndex}", Color.Cyan);

                // Get proxy if needed
                ProxyInfo? proxy = null;
                if (useProxy)
                {
                    proxy = _proxyManager.GetProxy();
                    if (proxy == null)
                    {
                        LogMessage($"Không có proxy cho tài khoản #{accountIndex}", Color.Yellow);
                    }
                }

                // Register account
                using var bot = new RegistrationBot(proxy);
                var result = await bot.RegisterAccountAsync(selectors, manualAccount);

                // Save result
                bot.SaveResult(result);

                // Update UI
                this.Invoke(() =>
                {
                    if (result.Success)
                    {
                        _successfulRegistrations++;
                        LogMessage($"✓ Tài khoản #{accountIndex}: {result.Account.Username} - THÀNH CÔNG", Color.Green);
                        lstResults.Items.Add($"✓ {result.Account.Username} - {result.Account.Password}");
                    }
                    else
                    {
                        _failedRegistrations++;
                        LogMessage($"✗ Tài khoản #{accountIndex}: THẤT BẠI - {result.ErrorMessage}", Color.Red);
                        lstResults.Items.Add($"✗ {result.Account.Username} - {result.ErrorMessage}");
                    }

                    progressBar.Value++;
                    UpdateStats();
                    UpdateProgress();
                });

                // Release proxy
                if (proxy != null)
                {
                    _proxyManager.ReleaseProxy(proxy);
                }
            }
            finally
            {
                semaphore.Release();
            }
        }

        private void UpdateStats()
        {
            var total = _successfulRegistrations + _failedRegistrations;
            var successRate = total > 0 ? (_successfulRegistrations * 100.0 / total) : 0;
            lblStats.Text = $"Thành công: {_successfulRegistrations} | Thất bại: {_failedRegistrations} | Tỷ lệ: {successRate:F1}%";
        }

        private void UpdateProgress()
        {
            var completed = progressBar.Value;
            var total = progressBar.Maximum;
            lblProgress.Text = $"Đã hoàn thành: {completed}/{total} tài khoản";
        }

        private void LogMessage(string message, Color color)
        {
            if (rtbLog.InvokeRequired)
            {
                rtbLog.Invoke(() => LogMessage(message, color));
                return;
            }

            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            rtbLog.SelectionStart = rtbLog.TextLength;
            rtbLog.SelectionLength = 0;
            rtbLog.SelectionColor = Color.Gray;
            rtbLog.AppendText($"[{timestamp}] ");
            rtbLog.SelectionColor = color;
            rtbLog.AppendText($"{message}\n");
            rtbLog.ScrollToCaret();

            // Keep only last 1000 lines
            if (rtbLog.Lines.Length > 1000)
            {
                var lines = rtbLog.Lines.Skip(100).ToArray();
                rtbLog.Lines = lines;
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_isRunning)
            {
                var result = MessageBox.Show("Quá trình đăng ký đang chạy. Bạn có muốn dừng và thoát?",
                    "Xác nhận", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }

                _isRunning = false;
            }

            _proxyManager?.Dispose();
            Logger.CloseAndFlush();
            base.OnFormClosing(e);
        }
    }
}
