{"folders": [{"name": "ChromeAutoManager", "path": "."}], "settings": {"dotnet.defaultSolution": "chorme auto.sln", "files.exclude": {"**/bin": true, "**/obj": true, "**/__pycache__": true, "**/logs": true, "**/screenshots": true, "**/chrome_profiles": true, "**/temp_profile_*": true, "**/*.log": true, "**/successful_accounts.txt": true, "**/failed_accounts.txt": true, "**/working_proxies.txt": true, "**/page_source*.html": true, "**/debug_page_source.html": true, "**/input_analysis.json": true, "**/username_counter.txt": true, "**/config.json": true}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.cs": "${capture}.Designer.cs, ${capture}.resx", "*.csproj": "*.csproj.user", "*.sln": "*.sln.DotSettings, *.sln.DotSettings.user"}, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "csharp.semanticHighlighting.enabled": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "files.associations": {"*.cs": "csharp", "*.csproj": "xml", "*.sln": "text"}}, "extensions": {"recommendations": ["ms-dotnettools.csharp", "ms-dotnettools.vscode-dotnet-runtime", "ms-vscode.vscode-json", "ms-vscode.powershell"]}, "launch": {"version": "0.2.0", "configurations": [{"name": "Launch Chrome Auto Manager", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net6.0-windows/ChromeAutoManager.exe", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false}]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/ChromeAutoManager.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/ChromeAutoManager.csproj", "--configuration", "Release", "--output", "${workspaceFolder}/publish"], "problemMatcher": "$msCompile"}]}}