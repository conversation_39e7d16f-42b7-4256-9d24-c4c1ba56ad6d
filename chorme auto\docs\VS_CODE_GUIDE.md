# 🚀 Visual Studio Code Development Guide

Hướng dẫn chi tiết sử dụng Visual Studio Code để phát triển Chrome Auto Manager.

## 📋 Yêu cầu

### Extensions cần thiết
VS Code sẽ tự động đề xuất cài đặt các extensions sau:

- **C# for Visual Studio Code** - Hỗ trợ C# IntelliSense
- **.NET Install Tool** - Quản lý .NET runtime
- **JSON** - Hỗ trợ JSON formatting
- **PowerShell** - Hỗ trợ PowerShell scripts

### Software cần thiết
- **Visual Studio Code** (latest)
- **.NET 6.0 SDK** hoặc cao hơn
- **Git** (optional)

## 🏗️ Cấu trúc project đã được tối ưu

```
ChromeAutoManager/
├── 📁 .vscode/              # VS Code configuration
│   ├── settings.json        # Workspace settings
│   ├── launch.json          # Debug configuration
│   ├── tasks.json           # Build tasks
│   └── extensions.json      # Recommended extensions
├── 📁 Core/                 # Core classes
├── 📁 Models/               # Data models
├── 📁 Services/             # Business logic
├── 📁 Forms/                # GUI forms
├── 📁 docs/                 # Documentation
├── 📁 scripts/              # Build scripts
├── 📁 archive/              # Old Python code
├── 📄 .editorconfig         # Code formatting rules
├── 📄 .gitignore           # Git ignore rules
├── 📄 ChromeAutoManager.csproj
├── 📄 chorme auto.sln
└── 📄 Program.cs
```

## 🚀 Quick Start với VS Code

### 1. Mở project
```bash
# Mở VS Code trong thư mục project
code "chorme auto"

# Hoặc từ VS Code: File > Open Folder
```

### 2. Cài đặt extensions
- VS Code sẽ tự động đề xuất extensions cần thiết
- Click **"Install All"** trong notification popup
- Hoặc mở **Extensions** panel (Ctrl+Shift+X) và search "C#"

### 3. Build project
```bash
# Sử dụng Command Palette (Ctrl+Shift+P)
> .NET: Build

# Hoặc sử dụng Terminal (Ctrl+`)
dotnet build
```

### 4. Run project
```bash
# Sử dụng Command Palette
> .NET: Run

# Hoặc nhấn F5 để debug
# Hoặc Ctrl+F5 để run without debugging
```

## 🔧 VS Code Features được cấu hình

### IntelliSense & Code Completion
- ✅ Auto-completion cho C# code
- ✅ Error highlighting real-time
- ✅ Import suggestions
- ✅ Code navigation (Go to Definition)

### Debugging
- ✅ Breakpoints
- ✅ Variable inspection
- ✅ Call stack
- ✅ Debug console

### Code Formatting
- ✅ Format on save
- ✅ Organize imports on save
- ✅ EditorConfig support
- ✅ Consistent code style

### File Management
- ✅ File nesting (Designer files)
- ✅ Hide build outputs
- ✅ Hide temporary files
- ✅ Smart file filtering

## ⌨️ Keyboard Shortcuts

### Building & Running
- **F5** - Start Debugging
- **Ctrl+F5** - Run Without Debugging
- **Ctrl+Shift+B** - Build
- **Ctrl+Shift+P** - Command Palette

### Code Navigation
- **F12** - Go to Definition
- **Ctrl+F12** - Go to Implementation
- **Shift+F12** - Find All References
- **Ctrl+T** - Go to Symbol in Workspace

### Code Editing
- **Ctrl+Space** - Trigger IntelliSense
- **Ctrl+.** - Quick Fix
- **Alt+Shift+F** - Format Document
- **Ctrl+K, Ctrl+F** - Format Selection

### File Management
- **Ctrl+P** - Quick Open File
- **Ctrl+Shift+E** - Explorer Panel
- **Ctrl+`** - Terminal Panel

## 🛠️ Available Tasks

Sử dụng **Terminal > Run Task** hoặc **Ctrl+Shift+P > Tasks: Run Task**:

- **build** - Build Debug version
- **build-release** - Build Release version
- **clean** - Clean build outputs
- **restore** - Restore NuGet packages
- **publish** - Publish for deployment
- **watch** - Watch for changes and auto-rebuild

## 🐛 Debugging

### Debug Configuration
Đã cấu hình sẵn 2 debug profiles:

1. **Launch Chrome Auto Manager** - Debug version
2. **Launch Chrome Auto Manager (Release)** - Release version

### Debug Features
- ✅ Set breakpoints bằng cách click vào line number
- ✅ Inspect variables trong **Variables** panel
- ✅ Watch expressions trong **Watch** panel
- ✅ View call stack trong **Call Stack** panel
- ✅ Debug console để execute commands

### Debug Tips
```csharp
// Sử dụng Debug.WriteLine để output debug info
System.Diagnostics.Debug.WriteLine("Debug message");

// Sử dụng conditional breakpoints
// Right-click breakpoint > Edit Breakpoint > Add condition
```

## 📝 Code Editing Tips

### Snippets
- **ctor** - Constructor
- **prop** - Property
- **propfull** - Property with backing field
- **class** - Class template
- **interface** - Interface template

### Refactoring
- **F2** - Rename symbol
- **Ctrl+.** - Quick actions and refactorings
- **Ctrl+R, Ctrl+R** - Rename all occurrences

### Code Generation
- **Ctrl+.** trên class name để generate:
  - Constructor
  - Properties
  - Equals/GetHashCode
  - ToString

## 🔍 Search & Replace

### Global Search
- **Ctrl+Shift+F** - Search in all files
- **Ctrl+Shift+H** - Replace in all files
- **F3/Shift+F3** - Find next/previous

### Advanced Search
- Use regex với **Alt+R**
- Case sensitive với **Alt+C**
- Whole word với **Alt+W**

## 📦 Package Management

### NuGet Packages
```bash
# Add package
dotnet add package PackageName

# Remove package
dotnet remove package PackageName

# Update packages
dotnet restore
```

### Package Manager UI
- Install **NuGet Package Manager** extension
- **Ctrl+Shift+P** > **NuGet Package Manager**

## 🔧 Troubleshooting

### Common Issues

#### 1. IntelliSense không hoạt động
```bash
# Restart OmniSharp
Ctrl+Shift+P > "OmniSharp: Restart OmniSharp"

# Reload window
Ctrl+Shift+P > "Developer: Reload Window"
```

#### 2. Build errors
```bash
# Clean và rebuild
dotnet clean
dotnet restore
dotnet build
```

#### 3. Debug không start
- Kiểm tra launch.json configuration
- Đảm bảo project đã build thành công
- Check Output panel cho error messages

#### 4. Extensions không load
- **Ctrl+Shift+P** > **Extensions: Reload Extensions**
- Restart VS Code
- Check internet connection

### Performance Tips
- Exclude large folders trong settings.json
- Disable unused extensions
- Use workspace settings thay vì global settings
- Close unused tabs

## 📚 Useful Extensions (Optional)

### Productivity
- **GitLens** - Enhanced Git capabilities
- **Bracket Pair Colorizer** - Colorize matching brackets
- **Auto Rename Tag** - Auto rename paired tags
- **Path Intellisense** - Autocomplete filenames

### Themes
- **One Dark Pro** - Popular dark theme
- **Material Icon Theme** - Better file icons
- **Dracula Official** - Dracula theme

### Code Quality
- **SonarLint** - Code quality analysis
- **Code Spell Checker** - Spell checking
- **TODO Highlight** - Highlight TODO comments

## 🎯 Best Practices

### Code Organization
- Sử dụng folders để group related files
- Follow naming conventions
- Add XML documentation comments
- Use regions để organize large files

### Git Integration
```bash
# Initialize git (if not already)
git init
git add .
git commit -m "Initial commit"
```

### Code Style
- Follow .editorconfig rules
- Use consistent naming
- Add comments cho complex logic
- Keep methods small và focused

---

**Happy Coding!** 🚀

Với setup này, bạn có thể phát triển Chrome Auto Manager một cách hiệu quả với VS Code!
